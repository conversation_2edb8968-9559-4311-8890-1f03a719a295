package auth

import (
	"fmt"
	"log"
	"log/slog"

	"github.com/MicahParks/keyfunc/v3"
)

func GetJWKS(awsRegion string, cognitoUserPoolId string) keyfunc.Keyfunc {

	jwksURL := fmt.Sprintf("https://cognito-idp.%s.amazonaws.com/%s/.well-known/jwks.json", awsRegion, cognitoUserPoolId)
	slog.Info("Fetching JWKS", "aws_region", awsRegion, "cognito_user_pool_id", cognitoUserPoolId, "url", jwksURL)

	// Create the JWKS from the resource at the given URL.
	jwks, err := keyfunc.NewDefault([]string{jwksURL})
	if err != nil {
		log.Fatalf("Failed to get the JWKS from the given URL.\nError: %s", err)
	}

	return jwks
}
