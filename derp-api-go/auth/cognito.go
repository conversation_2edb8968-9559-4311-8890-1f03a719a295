package auth

import (
	"context"
	"slices"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go-v2/service/cognitoidentityprovider/types"
	"github.com/google/uuid"
)

type CognitoService struct {
	client     *cognitoidentityprovider.Client
	userPoolID string
}

func NewCognitoService(cfg aws.Config, userPoolID string) (*CognitoService, error) {

	client := cognitoidentityprovider.NewFromConfig(cfg)

	return &CognitoService{
		client:     client,
		userPoolID: userPoolID,
	}, nil
}

func (c *CognitoService) ListUserGroups(username string) ([]string, error) {
	input := &cognitoidentityprovider.AdminListGroupsForUserInput{
		UserPoolId: &c.userPoolID,
		Username:   &username,
	}

	resp, err := c.client.AdminListGroupsForUser(context.TODO(), input)
	if err != nil {
		return nil, err
	}

	userGroups := make([]string, len(resp.Groups))
	for i, group := range resp.Groups {
		userGroups[i] = *group.GroupName
	}

	return userGroups, nil
}

type UserResponse struct {
	Email      string    `json:"email"`
	FamilyName string    `json:"family_name"`
	GivenName  string    `json:"given_name"`
	Sub        uuid.UUID `json:"sub"`
	TestAdmin  bool      `json:"test_admin"`
	UserAdmin  bool      `json:"user_admin"`
}

func (c *CognitoService) GetUser(username string) (*UserResponse, error) {

	userResponse := new(UserResponse)
	respUser, err := c.client.AdminGetUser(context.TODO(), &cognitoidentityprovider.AdminGetUserInput{
		UserPoolId: &c.userPoolID,
		Username:   &username,
	})

	if err != nil {
		return nil, err
	}

	for _, userAttributes := range respUser.UserAttributes {
		if *userAttributes.Name == "email" {
			userResponse.Email = *userAttributes.Value
			userGroups, err := c.ListUserGroups(*userAttributes.Value)
			userResponse.TestAdmin = slices.Contains(userGroups, "test_admins")
			userResponse.UserAdmin = slices.Contains(userGroups, "user_admins")

			if err != nil {
				return nil, err
			}

		}
		if *userAttributes.Name == "family_name" {
			userResponse.FamilyName = *userAttributes.Value
		}
		if *userAttributes.Name == "given_name" {
			userResponse.GivenName = *userAttributes.Value
		}
		if *userAttributes.Name == "sub" {
			parsedUUID, err := uuid.Parse(*userAttributes.Value)
			if err != nil {
				return nil, err
			}
			userResponse.Sub = parsedUUID
		}
	}

	return userResponse, nil
}

func (c *CognitoService) AddUserToGroup(username, groupName string) error {
	input := &cognitoidentityprovider.AdminAddUserToGroupInput{
		UserPoolId: &c.userPoolID,
		Username:   &username,
		GroupName:  &groupName,
	}

	_, err := c.client.AdminAddUserToGroup(context.TODO(), input)
	return err
}

func (c *CognitoService) RemoveUserFromGroup(username, groupName string) error {
	input := &cognitoidentityprovider.AdminRemoveUserFromGroupInput{
		UserPoolId: &c.userPoolID,
		Username:   &username,
		GroupName:  &groupName,
	}

	_, err := c.client.AdminRemoveUserFromGroup(context.TODO(), input)
	return err
}

func (c *CognitoService) ListAllUsers() ([]UserResponse, error) {
	var usersResponse []UserResponse
	input := &cognitoidentityprovider.ListUsersInput{
		UserPoolId: &c.userPoolID,
	}

	paginator := cognitoidentityprovider.NewListUsersPaginator(c.client, input)

	for paginator.HasMorePages() {
		page, err := paginator.NextPage(context.TODO())
		if err != nil {
			return nil, err
		}

		for _, user := range page.Users {

			var userResponse UserResponse
			for _, userAttributes := range user.Attributes {
				if *userAttributes.Name == "email" {
					userResponse.Email = *userAttributes.Value
					userGroups, err := c.ListUserGroups(*userAttributes.Value)
					userResponse.TestAdmin = slices.Contains(userGroups, "test_admins")
					userResponse.UserAdmin = slices.Contains(userGroups, "user_admins")

					if err != nil {
						return nil, err
					}

				}
				if *userAttributes.Name == "family_name" {
					userResponse.FamilyName = *userAttributes.Value
				}
				if *userAttributes.Name == "given_name" {
					userResponse.GivenName = *userAttributes.Value
				}
				if *userAttributes.Name == "sub" {
					parsedUUID, err := uuid.Parse(*userAttributes.Value)
					if err != nil {
						return nil, err
					}
					userResponse.Sub = parsedUUID
				}
			}
			usersResponse = append(usersResponse, userResponse)
		}
	}
	return usersResponse, nil
}

func (c *CognitoService) InviteUser(username string) error {
	input := &cognitoidentityprovider.AdminCreateUserInput{
		UserPoolId:             &c.userPoolID,
		Username:               &username,
		DesiredDeliveryMediums: []types.DeliveryMediumType{"EMAIL"},
	}

	_, err := c.client.AdminCreateUser(context.TODO(), input)
	return err
}

func (c *CognitoService) DeleteUser(username string) error {
	input := &cognitoidentityprovider.AdminDeleteUserInput{
		UserPoolId: &c.userPoolID,
		Username:   &username,
	}

	_, err := c.client.AdminDeleteUser(context.TODO(), input)
	return err
}

func (c *CognitoService) SearchUsers(username string) ([]string, error) {
	var foundUserEmails []string
	input := &cognitoidentityprovider.ListUsersInput{
		UserPoolId:      &c.userPoolID,
		AttributesToGet: []string{"email"},
	}

	paginator := cognitoidentityprovider.NewListUsersPaginator(c.client, input)

	for paginator.HasMorePages() {
		page, err := paginator.NextPage(context.TODO())
		if err != nil {
			return nil, err
		}

		for _, user := range page.Users {

			for _, userAttributes := range user.Attributes {
				if *userAttributes.Name == "email" {
					foundUserEmails = append(foundUserEmails, *userAttributes.Value)
				}
			}
		}
	}
	return foundUserEmails, nil
}
