SELECT tests.id,findings.base_severity,COUNT(*)
FROM tests
INNER JOIN user_tests on tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id
FULL JOIN phases ON tests.id = phases.test_id
FULL JOIN findings ON phases.id = findings.phase_id
WHERE users.id = '4a6a9275-d35f-4e0d-946c-416a19fc479d'
GROUP BY tests.id, findings.base_severity;


SELECT tests.id, tests.engagement_number, tests.name, phases.number, phases.name, findings.*
FROM tests
INNER JOIN user_tests on tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id
INNER JOIN phases ON tests.id = phases.test_id
INNER JOIN findings ON phases.id = findings.phase_id
WHERE users.id = '4a6a9275-d35f-4e0d-946c-416a19fc479d' AND tests.id = '4a6a9275-d35f-4e0d-946c-416a19fc479d';

SELECT tests.*, users.email
FROM tests
INNER JOIN user_tests on tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id;

SELECT tests.*, users.email
FROM tests
INNER JOIN user_tests ON tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id
WHERE tests.id = '4a6a9275-d35f-4e0d-946c-416a19fc479d';

UPDATE user_tests 
SET is_test_archived = false
WHERE user_tests.user_id = '4a6a9275-d35f-4e0d-946c-416a19fc479d' AND user_tests.test_id = '4a6a9275-d35f-4e0d-946c-416a19fc479d';


SELECT findings.*, scoring_systems.name AS scoring_system_name, tests.engagement_number, phases.number
FROM findings
JOIN phases ON phases.id = findings.phase_id
JOIN tests ON tests.id = phases.test_id
JOIN user_tests ON user_tests.test_id = tests.id
JOIN users ON users.id = user_tests.user_id
JOIN scoring_systems ON scoring_systems.id = tests.scoring_system_id
WHERE users.id = '4a6a9275-d35f-4e0d-946c-416a19fc479d'
AND findings.number = 1
AND phases.number = 1
AND tests.id = '4a6a9275-d35f-4e0d-946c-416a19fc479d';
