package middlewares

import (
	"fmt"
	"log/slog"
	"net/http"
	"slices"
	"strings"
	"time"

	"github.com/MicahParks/keyfunc/v3"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// Gin middleware for verifying an incoming Cognito JWT, embedded in the request headers
// https://docs.aws.amazon.com/cognito/latest/developerguide/amazon-cognito-user-pools-using-tokens-verifying-a-jwt.html
func CognitoAuthMiddleware(requiredTokenUse string, awsDefaultRegion string, cognitoUserPoolId string, cognitoAppClientId string, jwks keyfunc.Keyfunc, isTestAdminRequired bool, isUserAdminRequired bool) gin.HandlerFunc {
	return func(c *gin.Context) {

		// Retrieve JWT from the "Authorization" header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			slog.Error("Invalid Authorization header")
			c.JSO<PERSON>(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		splitToken := strings.Split(authHeader, "Bearer ")

		if len(splitToken) != 2 {
			slog.Error("Invalid Authorization header")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		tokenString := splitToken[1]

		if tokenString == "" {
			slog.Error("Invalid Authorization header")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// * Verify the signature of the JWT
		// * Verify that the algorithm used is RS256
		// * Verify that the 'exp' claim exists in the token
		// * Verification of audience 'aud' is taken care later when we examine if the
		//   token is 'id' or 'access'
		// * The issuer (iss) claim should match your user pool. For example, a user
		//   pool created in the eu-west-2 region
		//   will have the following iss value: https://cognito-idp.us-east-1.amazonaws.com/<userpoolID>.
		token, err := jwt.Parse(tokenString,
			jwks.Keyfunc,
			jwt.WithValidMethods([]string{"RS256"}),
			jwt.WithExpirationRequired(),
			jwt.WithIssuer(fmt.Sprintf("https://cognito-idp.%s.amazonaws.com/%s", awsDefaultRegion, cognitoUserPoolId)))
		if err != nil || !token.Valid {
			slog.Error("Invalid JWT in header")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// Attempt to parse the JWT claims
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			slog.Error("Cannot map JWT claims")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// Compare the "exp" claim to the current time
		expClaim, err := claims.GetExpirationTime()
		if err != nil {
			slog.Error("Cannot map 'exp' attribute in token")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		if expClaim.Unix() < time.Now().Unix() {
			slog.Error("Invalid expiration time")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// Check the token_use claim.
		// If you are only accepting the access token in your web API operations, its value must be access.
		// If you are only using the ID token, its value must be id.
		// If you are using both ID and access tokens, the token_use claim must be either id or access.
		tokenUseClaim, ok := claims["token_use"].(string)
		if !ok {
			slog.Error("Cannot map 'token_use' attribute in token")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		if tokenUseClaim != requiredTokenUse {
			slog.Error("'token_use' attribute different from required token use")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// "sub" claim exists in both ID and Access tokens
		subClaim, err := claims.GetSubject()
		if err != nil {
			slog.Error("Cannot map 'sub' attribute from token")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		c.Set("username", subClaim)

		// The "aud" claim in an ID token and the "client_id" claim in an access token should match the app
		// client ID that was created in the Amazon Cognito user pool.
		var appClientIdClaim string
		if tokenUseClaim == "id" {
			// "email", "given_name" and "family_name" claims exist only in a Cognito ID token
			c.Set("email", claims["email"])
			c.Set("given_name", claims["given_name"])
			c.Set("family_name", claims["family_name"])

			audienceClaims, err := claims.GetAudience()
			if err != nil {
				slog.Error("Cannot map 'aud' attribute from token")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
				c.Abort()
				return
			}
			appClientIdClaim = audienceClaims[0]

		} else if tokenUseClaim == "access" {
			clientIdClaim, ok := claims["client_id"].(string)
			if !ok {
				slog.Error("Cannot map 'client_id' attribute from token")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
				c.Abort()
				return
			}
			appClientIdClaim = clientIdClaim
		} else {
			slog.Error("Invalid 'token_use' attribute")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}
		if appClientIdClaim != cognitoAppClientId {
			slog.Error("Mismatch in 'client_id' attribute")
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			c.Abort()
			return
		}

		// Retrieve any Cognito user groups that the user belongs to
		userGroupsAttribute, ok := claims["cognito:groups"]
		userGroupsClaims := make([]string, 0)
		if ok {
			switch x := userGroupsAttribute.(type) {
			case []interface{}:
				for _, e := range x {
					userGroupsClaims = append(userGroupsClaims, e.(string))
				}
			default:
				slog.Error("Error retrieving 'user_groups' attribute")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
				c.Abort()
				return
			}
		}

		c.Set("groups", userGroupsClaims)

		if isTestAdminRequired {
			if !slices.Contains(userGroupsClaims, "test_admins") {
				slog.Error("Test admin access required for endpoint")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
				c.Abort()
				return
			}
		}

		if isUserAdminRequired {
			if !slices.Contains(userGroupsClaims, "user_admins") {
				slog.Error("User admin access required for endpoint")
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}
