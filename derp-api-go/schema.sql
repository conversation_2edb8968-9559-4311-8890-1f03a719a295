create table users
(
    id    uuid    not null
        primary key,
    email varchar not null
        unique
);

create table scoring_systems
(
    id   serial
        primary key,
    name varchar not null
        unique
);


create table tests
(
    id                  uuid         not null
        primary key,
    engagement_number   integer      not null,
    scoring_system_id   integer      not null
        references scoring_systems,
    name                varchar      not null,
    time_of_last_export timestamp    not null,
    export_status       exportstatus not null,
    export_error        varchar
);

create table user_tests
(
    test_id          uuid
        references tests,
    user_id          uuid
        references users,
    is_test_archived boolean not null default false
);


create table phases
(
    id      serial
        primary key,
    guid    uuid    not null,
    number  integer not null,
    test_id uuid    not null
        references tests,
    name    varchar,
    constraint uc_guid_test_id
        unique (guid, test_id)
);


create table findings
(
    id                  uuid                                        not null
        primary key,
    number              integer                                     not null,
    phase_id            integer                                     not null
        references phases,
    name                varchar,
    base_severity       varchar                                     not null,
    vector_string       varchar                                     not null,
    status              findingstatus default 'OPEN'::findingstatus not null,
    description         text,
    recommendation      text,
    supporting_material text
);


create table associated_hosts
(
    id         serial
        primary key,
    finding_id uuid    not null
        references findings,
    ip_address inet    not null,
    network    varchar not null,
    port       integer not null,
    protocol   varchar not null
);


create type exportstatus as enum ('OK', 'ERROR');

create type findingstatus as enum ('OPEN', 'CLOSED', 'FALSE_POSITIVE');
