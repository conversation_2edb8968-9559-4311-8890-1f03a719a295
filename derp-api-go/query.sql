-- name: GetF<PERSON>ingsCountsPerTestForUser :many
SELECT tests.*, findings.base_severity, COUNT(*)
FROM tests
INNER JOIN user_tests on tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id
FULL JOIN phases ON tests.id = phases.test_id
FULL JOIN findings ON phases.id = findings.phase_id
WHERE users.id = $1
GROUP BY tests.id, findings.base_severity;


-- name: GetTestDetails :many
SELECT sqlc.embed(tests), sqlc.embed(findings)
FROM tests
JOIN user_tests on tests.id = user_tests.test_id
JOIN users ON users.id = user_tests.user_id
JOIN phases ON tests.id = phases.test_id
JOIN findings ON phases.id = findings.phase_id
WHERE users.id = $1 AND tests.id = $2;


-- name: GetTestsWithAssignedUsers :many
SELECT tests.*, users.email
FROM tests
INNER JOIN user_tests on tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id;


-- name: GetTestWithAssignedUsers :many
SELECT tests.*, users.email
FROM tests
INNER JOIN user_tests ON tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id
WHERE tests.id = $1;

-- name: GetTestForID :one
SELECT tests.*
FROM tests
WHERE tests.id = $1;

-- name: GetUserForEmail :one
SELECT users.*
FROM users
WHERE users.email = $1;

-- name: DeleteTest :exec
DELETE FROM tests WHERE tests.id = $1;

-- name: AssignTestToUser :exec
INSERT INTO user_tests (test_id, user_id) VALUES ($1, $2);

-- name: RemoveUserFromTest :exec
DELETE FROM user_tests WHERE user_tests.user_id = $1 AND user_tests.test_id = $2;

-- name: ArchiveTest :exec
UPDATE user_tests 
SET is_test_archived = true
WHERE user_tests.user_id = $1 AND user_tests.test_id = $2;

-- name: UnarchiveTest :exec
UPDATE user_tests 
SET is_test_archived = true
WHERE user_tests.user_id = $1 AND user_tests.test_id = $2;


-- name: GetFinding :one
SELECT sqlc.embed(findings), sqlc.embed(scoring_systems), sqlc.embed(tests), sqlc.embed(phases)
FROM findings
JOIN phases ON phases.id = findings.phase_id
JOIN tests ON tests.id = phases.test_id
JOIN user_tests ON user_tests.test_id = tests.id
JOIN users ON users.id = user_tests.user_id
JOIN scoring_systems ON scoring_systems.id = tests.scoring_system_id
WHERE users.id = $1
AND findings.number = $2
AND phases.number = $3
AND tests.id = $4;
