package routes

import (
	"derp-api/auth"
	"derp-api/db"
	"derp-api/middlewares"

	"github.com/MicahParks/keyfunc/v3"
	"github.com/gin-gonic/gin"
)

func addAdminTestsRoutes(rg *gin.RouterGroup, jwks keyfunc.Keyfunc, cognitoService auth.CognitoService, awsDefaultRegion string, cognitoUserPoolId string, cognitoAppClientId string) {
	adminTests := rg.Group("/admin/tests")

	adminTests.GET("", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, true, false), func(c *gin.Context) {
		results := db.GetTestsWithAssignedUsers()
		c.JSON(200, gin.H{
			"data": results,
		})
	})

	adminTests.GET("/:documentGuid", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, true, false), func(c *gin.Context) {
		documentGuid := c.Param("documentGuid")
		result, err := db.GetTestWithAssignedUsers(documentGuid)
		if result == nil {
			c.JSON(404, gin.H{
				"data": err.Error(),
			})
		} else {
			c.JSON(200, gin.H{
				"data": result,
			})
		}
	})

	// Assign user to test
	adminTests.POST("/:documentGuid/users/:userEmail", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, true, false), func(c *gin.Context) {

		documentGuid := c.Param("documentGuid")
		userEmail := c.Param("userEmail")

		// Before even attempting to add associate a user to a test, check if they exist in the Cognito User Pool
		user, err := cognitoService.GetUser(userEmail)
		if err != nil {
			c.JSON(500, gin.H{
				"detail": "Could not assign user to test",
			})
			return
		}

		// If the user exists in the Cognito User Pool, attempt to assign them to a Test
		err = db.AssignUserToTest(documentGuid, user.Sub, user.Email)
		if err != nil {
			c.JSON(400, gin.H{
				"detail": err.Error(),
			})
		} else {
			c.JSON(200, gin.H{
				"data": "Assigned user",
			})
		}
	})

	// Remove user from test
	adminTests.DELETE("/:documentGuid/users/:userEmail", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, true, false), func(c *gin.Context) {
		documentGuid := c.Param("documentGuid")
		userEmail := c.Param("userEmail")
		err := db.RemoveUserFromTest(documentGuid, userEmail)
		if err != nil {
			c.JSON(404, gin.H{
				"data": err.Error(),
			})
		} else {
			c.JSON(200, gin.H{
				"data": "Removed user",
			})
		}
	})
}
