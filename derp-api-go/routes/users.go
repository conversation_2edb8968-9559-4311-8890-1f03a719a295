package routes

import (
	"derp-api/middlewares"
	"net/http"

	"github.com/MicahParks/keyfunc/v3"
	"github.com/gin-gonic/gin"
)

func addUserRoutes(rg *gin.RouterGroup, jwks keyfunc.Keyfunc, awsDefaultRegion string, cognitoUserPoolId string, cognitoAppClientId string) {
	users := rg.Group("/users")

	users.GET("/profile", middlewares.CognitoAuthMiddleware("id", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, false), func(c *gin.Context) {

		email, _ := c.Get("email")
		givenName, _ := c.Get("given_name")
		familyName, _ := c.Get("family_name")
		groups, _ := c.Get("groups")

		c.JSON(http.StatusOK, gin.H{
			"email":       email,
			"given_name":  givenName,
			"family_name": familyName,
			"groups":      groups,
		})
	})
}
