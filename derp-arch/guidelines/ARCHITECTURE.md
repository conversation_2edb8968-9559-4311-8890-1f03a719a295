# Derp components and architecture
- [Derp components and architecture](#derp-components-and-architecture)
  - [Current infrastructure architecture](#current-infrastructure-architecture)
  - [Backend](#backend)
    - [Organisation of endpoints](#organisation-of-endpoints)
    - [CORS](#cors)
    - [How authorisation/authentication from Cognito works](#how-authorisationauthentication-from-cognito-works)
    - [Database](#database)
      - [Running a local PostgreSQL](#running-a-local-postgresql)
      - [Modelling](#modelling)
      - [Usage](#usage)
      - [Sample data](#sample-data)
      - [Connectivity to AWS deployed database through an EC2 bastion](#connectivity-to-aws-deployed-database-through-an-ec2-bastion)
      - [Migrations](#migrations)
  - [User interface](#user-interface)
  - [How routing works](#how-routing-works)
    - [Layouts and pages](#layouts-and-pages)
    - [How Amplify in the UI works](#how-amplify-in-the-ui-works)
    - [Interacting with the API through HTTP calls](#interacting-with-the-api-through-http-calls)
  - [Infrastructure-as-code - CDK](#infrastructure-as-code---cdk)
  - [What needs to be done](#what-needs-to-be-done)
  
This guide also [exists in CodeCommit](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-arch/guidelines/ARCHITECTURE.md?region=eu-west-2).

Everything in the current architecture can be run with the following four components installed on your machine. Everything will work natively in every major operating system, even Windows:

- [Python 3.11.\*](https://www.python.org/downloads/) and its dependency manager [Poetry (installed separately with `pip install poetry`)](https://python-poetry.org/), think of it like a modern `pip` that resembles `npm` a lot
- [Node.js 18.\* LTS](https://nodejs.org/) and `npm`
- [Docker Desktop](https://www.docker.com/products/docker-desktop/) for running the PostgreSQL database and containerising the API and UI
- [AWS CLI](https://aws.amazon.com/cli/)

All the code for the components mentioned in this guide, along with more detailed READMEs exist in the Accenture AWS Console, in [CodeCommit](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse?region=eu-west-2).

## Current infrastructure architecture

There have been a lot of discussions around moving to a subnet based infrastructure instead of having a VPC per tenant. My personal preference would be to use VPCs since it would provide complete network isolation and any accidental "crossing of wires". I understand though that creating a DMZ sitting in front of all tenants and then doing VPC peering might prove difficult to implement.

In any case, I wanted to clear up a bit the existing infra and what has been working for now. I had to delete some newly created Cloudfront distros, Route53 entries, certificates and load-balancers because they were interfering with the current implementation that "works" (everything is there we just need to solve CORS issues, which we need to do anyway), but whatever is needed please recreate it.

* There are [no Cloudfront distros](https://us-east-1.console.aws.amazon.com/cloudfront/v3/home?region=eu-west-2#/distributions). Some of the older entries were interfering with Route53 entries so I preferred to have a clean slate.
* [There are only 2 Hosted Zones](https://us-east-1.console.aws.amazon.com/route53/v2/hostedzones#). A public `zero-cool.co.uk` and a private `api.zero-cool.co.uk`, the public is associated with the UI, the private with the API
   ![Alt text](image-3.png)
* All components we have are in the private subnet of the VPC, the only public component is [the load balancer of the UI](https://eu-west-2.console.aws.amazon.com/ec2/home?region=eu-west-2#LoadBalancer:loadBalancerArn=arn:aws:elasticloadbalancing:eu-west-2:************:loadbalancer/app/derp-clientc-ui-alb/5b0b3dc5b66405bc;tab=listeners), not [the Fargate UI service it serves](https://eu-west-2.console.aws.amazon.com/ecs/v2/clusters/derp-clientc-cluster/services/derp-clientc-infra-stack-derpclientcuiserviceService4493653D-E9Pt1UGDCOCB/health?region=eu-west-2). That's the only visible entry-point from the outside world. Also, the load balancer and Fargate service of the API are in the private subnet too.
* `https://clientc.api.zero-cool.co.uk/` is not reachable from anything on the outside, you don't even now if it is valid unless you login to the UI and see calls being done to it, which we can also mitigate if we want
* UI and API HTTPS load balancers are associated with [the same certificate with SANs](https://eu-west-2.console.aws.amazon.com/acm/home?region=eu-west-2#/certificates/84e588f1-d4a9-4eda-9f77-89de10928de9) and it seems to work nicely, I do not receive invalid certificate errors.
  

## [Backend](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api?region=eu-west-2)
Our backend is in Python and [FastAPI](https://fastapi.tiangolo.com/).

In order to have a working local environment you need to have a `.env` file at the root of the project. I am pasting the real values that work with our current test client (clientc) here, but the generic schema is defined also [in the project's README](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api?region=eu-west-2):

```dotenv
# PostgreSQL
DB_NAME=derp
DB_USER=derp
DB_PASSWORD=derp
DB_HOST=localhost
DB_PORT=5432

# AWS
AWS_DEFAULT_REGION=eu-west-2
COGNITO_USER_POOL_ID=eu-west-2_hvtm7LQJe
COGNITO_APP_CLIENT_ID=2b8skb89208f9oc9i9ko3t7j41

# CORS
CORS_ORIGIN_URL=http://localhost:3000
```

You also need to configure your AWS CLI using `aws configure` with keys (access key, secret) from a IAM user having Cognito permissions. Feel free to replicate what I have done for [my IAM user](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/users/details/a.panagiotopoulos.cli?section=permissions) where I have assigned `AmazonCognitoPowerUser` as a policy. This is needed because the API [is effectively making boto3 calls to Cognito](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/cognito.py?region=eu-west-2).

### Organisation of endpoints
Except from [two top-level endpoints](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/main.py?region=eu-west-2&lines=54-54) `/healthcheck` and `/profile`, the rest have been organised under groups of routers:

![Alt text](image.png)

For example for all functionality the Admin Users, [we have specified a router](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/main.py?region=eu-west-2&lines=61-61):

```python
app.include_router(
    admin_users.router,
    prefix="/admin/users",
    tags=["admin, users"],
)
```

...and in it, [one of its endpoints](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/routers/admin_users.py?region=eu-west-2&lines=21-21) (omitting its docstring) which resolves to just `/admin/users` from the prefix declared above, since I have left the route name empty (""):

```python
@router.get("")
async def get_users(
    _=Depends(check_user_is_user_admin),
):
    try:
        users = await cognito.get_users()
    except CognitoError as e:
        raise HTTPException(status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    return {"data": users}
```

### CORS
In our current AWS deployment, the browser/client side of the Derp is effectively asking data from an API in `clientc.api.zero-cool.co.uk` while the UI is in `clientc.zero-cool.co.uk`. In order to do that we need to [allow CORS on the backend side from the UI](https://fastapi.tiangolo.com/tutorial/cors/).

[This is supposed to be properly configured in FastAPI](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/main.py?region=eu-west-2&lines=16-16). It works locally for me because the UI runs on `http://localhost:3000` while the API runs on `http://localhost:8000`, even different ports differentiate a domain.


### How authorisation/authentication from Cognito works

The API is accepting and validating JWT (Bearer Tokens) from Cognito. [Official documentation](https://docs.aws.amazon.com/cognito/latest/developerguide/amazon-cognito-user-pools-using-tokens-verifying-a-jwt.html#amazon-cognito-user-pools-using-tokens-manually-inspect) explains the theory behind validating a JWT in a backend app and how to verify the claims in it. [The API implements similar logic](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/jwt_bearer.py?region=eu-west-2&lines=36-36).

More specifically for this project, protection in the endpoints, so they can be accessible by the proper users, is done using [FastAPI Dependencies](https://fastapi.tiangolo.com/tutorial/dependencies/), a very elegant `Dependency Injection` system. It allows you to decorate your endpoints with whatever dependency logic you need (database session, authorisation, parameter checks), which should run before proceeding to run the actual endpoint, in our case JWT authorisation.

Repeating [the example above](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/routers/admin_users.py?region=eu-west-2&lines=21-21), you can see how it is used, and you can also check the rest of the endpoints that use the same mechanism:

```python
@router.get("")
async def get_users(
    _=Depends(check_user_is_user_admin),
):
```

There are currently four reusable "dependencies" declared in the app:

* [checks if the user is in the Cognito User Pool and at the same time returns their claims](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/dependencies.py?region=eu-west-2&lines=13-13)

* [checks if the user is in the User Admin group](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/dependencies.py?region=eu-west-2&lines=34-34)

* [checks if the user is in the Test Admin group](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/dependencies.py?region=eu-west-2&lines=67-67)

* [generic database dependency](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/dependencies.py?region=eu-west-2&lines=100-100) for when the endpoint needs to access the database, also seen as an example in the [official documentation](https://fastapi.tiangolo.com/tutorial/sql-databases/#create-a-dependency)

You can import and reuse them anywhere you define a new endpoint.

### Database

#### Running a local PostgreSQL
You don't need PostgreSQL installed natively in your environment. [It has been setup as a Docker container with persistent storage](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/compose.yml?region=eu-west-2&lines=13-13).

All you need to do is run from the root of the `derp-api` directory:
```bash
docker compose up db -d
```
...and it will be ready for you.

Before automatically creating the necessary tables using migrations, you just need to create manually a database to talk to. Just make sure it is the same name as the one you define in your `.env` file you have created in the `derp-api` directory as it is mentioned above.

#### Modelling
We use a very similar technology to Django ORM, [`SQLAlchemy`](https://www.sqlalchemy.org/) which is [documented and supported in FastAPI](https://fastapi.tiangolo.com/tutorial/sql-databases/). The implementation we have is very similar to the official documentation.

For example the `Test` model which creates a `tests` table [is defined as followed](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/models.py?region=eu-west-2&lines=31-31):

```python
class Test(Base):
    __tablename__ = "tests"

    id = Column(Integer, primary_key=True, index=True)
    test_id = Column(String, unique=True, nullable=False)
    name = Column(String, nullable=False)
    phases = relationship("Phase", back_populates="test")
    users = relationship(
        "User", secondary=tests_users_association, back_populates="tests"
    )
```

#### Usage
A lot of the current backend logic that uses `SQLAlchemy` is done in [`crud.py`](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/crud.py?region=eu-west-2), which was recently completely overhauled with the inclusion of `phases`.

You can also follow the [official documentation](https://fastapi.tiangolo.com/tutorial/sql-databases/#file-structure), since the backend uses almost similar file structure and architecture.

#### Sample data
I have also added some sample data in [a SQL script `test_data.sql` with insert statements](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/test_data.sql?region=eu-west-2). They create a `Context Risk Rating` system in the database, associate it with Summary and Detail attributes, and a couple of test cases with findings.

#### Connectivity to AWS deployed database through an EC2 bastion

This might be difficult to get right.

I have created [a small EC2 acting as a bastion to the private and isolated network in the VPC](https://eu-west-2.console.aws.amazon.com/ec2/home?region=eu-west-2#InstanceDetails:instanceId=i-002a37bd75223858f). You can use it to interact with the deployed database locally.

This requires you to establish an SSH tunnel. For that you need to first allow traffic on port 22 from your IP address in [the inbound rules of the instance](https://eu-west-2.console.aws.amazon.com/ec2/home?region=eu-west-2#ModifyInboundSecurityGroupRules:securityGroupId=sg-09d55f95840b2d933). 

![Alt text](image-5.png)


After that, the following command makes use of the SSH key associated with the instance, and effectively says: port 5431 on your local machine will act as a tunnel to the RDS Postgres database on port 5432 through the EC2 bastion.

```bash
ssh -i "derp-bastion-ssh-key.pem" -NL 5431:derp-clientc-infra-stack-derpclientcdb2c7e7c5c-tagdp1y3mk7p.ceadtzohrwyh.eu-west-2.rds.amazonaws.com:5432 <EMAIL> -v
```

You can then add connection details pointing to your `localhost:5431`, which effectively is a tunnel to the deployed database. [The username/password credentials currently are generated and exist in AWS Secrets Manager](https://eu-west-2.console.aws.amazon.com/secretsmanager/secret?name=derp-clientc-database-credentials-secret&region=eu-west-2)
![Alt text](image-4.png)

#### Migrations
Migration functionality is also added to the project with [Alembic](https://alembic.sqlalchemy.org/en/latest/), again very similar to Django and also [documented in the project's README](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api?region=eu-west-2).

The project already has a schema defined. After you have created a database, just run:

```bash
alembic upgrade head 
```

Whenever you make changes to the schema you can run the next command with a descriptive comment that will generate the migrations file, followed again by the previous command to apply it:

```bash
alembic revision --autogenerate -m "comment"
```

## [User interface](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-ui?region=eu-west-2)

Our UI is created using a React framework: [Next.js](https://nextjs.org/), which is [**the primary recommended way of scaffolding a React project according to the official React documentation**](https://react.dev/learn/start-a-new-react-project). This framework can also act as a full-stack framework with backend capabilities, but for the purposes of this project we are only using it as a pure React frontend app. 

We are also making incremental [use of React Server Components](https://nextjs.org/docs/getting-started/react-essentials#thinking-in-server-components). That means that parts of our app are returning already compiled HTML code instead of client-side JavaScript to be executed in the browser, improving its security, performance and app size bundles. There are still some components in the app that can also be transferred to this mental model, but this will require a bit more time and effort, when the UI will be more stable in terms of needed features. It might sound overkill to think that far ahead, and it might seem too early given that the UI contains a few screens now, but the benefits of all this will become more clear as the app grows in features.

I am giving some details and examples of client and server components in the app below, where I mention how to fetch data in each.

As a main architecture and file organisation we are using the `App Router` of `Next.js` which has been stable for months. You can read more in the [official Next.js documentation](https://nextjs.org/docs). Take care though to read online documentation related to the newest `App Router` and not the previous implementation of `Pages Router`. A lot of tutorials online and answers in StackOverflow still refer to the previous implementation.

![Alt text](image-1.png)

Similar to the API you need to create a `.env.local` file at the root of the project. I am pasting the real values that work with our current test client (clientc) here, but [the generic schema is defined also in the project's README](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-ui?region=eu-west-2):

```
NEXT_PUBLIC_USER_POOL_ID=eu-west-2_hvtm7LQJe
NEXT_PUBLIC_USER_POOL_WEB_CLIENT_ID=2b8skb89208f9oc9i9ko3t7j41
NEXT_PUBLIC_CLIENT_NAME=clientc

# Change for production
# NEXT_PUBLIC_API_BASE_URL=https://clientc.api.zero-cool.co.uk
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:8000
```
## How routing works
A quick example that implements the file-based `App Router` architecture in the project, its concept is also [shown in more detail in the official documentation](https://nextjs.org/docs/app/building-your-application/routing/defining-routes):

![Alt text](image-2.png)

The following structure resolves to an endpoint:

```bash
http://localhost:3000/tests/{testId}/findings/{findingId}
```

The highlighted `page.tsx` [contains the main React component that will be served when a user visits that page](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-ui/app/tests/%5Btest%5D/findings/%5Bfinding%5D/page.tsx?region=eu-west-2&lines=39-39).

### Layouts and pages
`layout.tsx` files contain components that will be shared between multiple pages as you go down the tree of routes.

For example [the top level/root `layout.tsx`](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-ui/app/layout.tsx?region=eu-west-2) contains the Accenture header and footer which remain stable and won't rerender when navigating through the rest of the pages. It is the very top level entrypoint of the app and [this is also where we wrap the app with Amplify and protect the pages from a client/UI perspective](https://ui.docs.amplify.aws/react/connected-components/authenticator#step-3-add-the-authenticator). 

```typescript
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      className={`${accentureMainFont.variable} ${accentureMainSemiboldFont.variable} ${accentureMainBoldFont.variable}`}
    >
      <body>
        <Authenticator formFields={formFields} components={components}>
          {({ signOut, user }) => (
            <div className="flex h-screen w-screen flex-col">
              <Header
                signOut={signOut}
                givenName={user?.attributes?.given_name}
                familyName={user?.attributes?.family_name}
              />
              <div className="flex-grow font-accenture-main">{children}</div>
              <Footer />
            </div>
          )}
        </Authenticator>
      </body>
    </html>
  );
}
```

### How Amplify in the UI works

First, let's emphasize and make clear that [**WE DO NOT USE AMPLIFY "THE AWS SERVICE OR COMPLETE  DEPLOYMENT PLATFORM"**](https://aws.amazon.com/amplify/) at this point in the project.

We are using [Amplify "the JavaScript library" with the Next.js App Router](https://docs.amplify.aws/lib/ssr/q/platform/js/#use-amplify-with-nextjs-app-router-app-directory) which essentially interacts with the Cognito User Pools we assign to the app [through environment variables taken from the Cognito User Pool in the AWS console](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-ui/app/awsExports.tsx?region=eu-west-2). If it makes it easier to reason about it, it should have been named something like `cognito-auth.js`.

### Interacting with the API through HTTP calls

There are two ways we are using to make HTTP calls to the API, depending on where we make the call from in the UI code. In effect though, using both ways, we need to add the proper JWT from Cognito as a Authorization header/Bearer Token in the call, so it can be read by the API.

* If we are in a [server component](https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating#fetching-data-on-the-server-with-fetch) like in [`/app/dashboard/page.tsx`](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-ui/app/dashboard/page.tsx?region=eu-west-2&lines=23-23), [we call `Amplify` with `withSSRContext` and `SSR.Auth`, also shown in the official documentation, to embed a JWT as a header in the call](https://docs.amplify.aws/lib/ssr/q/platform/js/#2-prepare-a-request-object-for-withssrcontext-to-perform-server-side-operations-that-require-authentication)


```typescript
Amplify.configure({ ...awsExports, ssr: true });

async function getTests(): Promise<Test[]> {
  const req = {
    headers: {
      cookie: headers().get("cookie"),
    },
  };

  const SSR = withSSRContext({ req });
  const token = (await SSR.Auth.currentSession()).getIdToken().getJwtToken();
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/tests`, {
    cache: "no-store",
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: "application/json",
    },
  });

  if (!res.ok) {
    // TODO handle more cases
    if (res.status === 404) {
      notFound();
    }
  }
  const { tests } = await res.json();
  return tests;
}
```

* If we are in a [client component](https://nextjs.org/docs/app/building-your-application/data-fetching/fetching-caching-and-revalidating#fetching-data-on-the-client-with-route-handlers) like in [/app/admin/tests/[test]/page.tsx](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-ui/app/admin/tests/%5Btest%5D/page.tsx?region=eu-west-2), [we call `Amplify` with just `Auth`](https://docs.amplify.aws/lib/restapi/authz/q/platform/js/#cognito-user-pools-authorization), and we can use whatever client-side mechanism we want like `useffect` or other third-party fetching libraries.

**!!!Warning!!! this is also where I am getting CORS errors in AWS but not locally**. Again repeating what is mentioned above: 

In AWS, the browser/client side of the Derp is effectively asking data from an API in `clientc.api.zero-cool.co.uk` while the UI is in `clientc.zero-cool.co.uk`. In order to do that we need to [allow CORS on the backend side from the UI](https://fastapi.tiangolo.com/tutorial/cors/).

[This is supposed to be properly configured in FastAPI](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/src/main.py?region=eu-west-2&lines=16-16). It works locally for me because the UI runs on `http://localhost:3000` while the API runs on `http://localhost:8000`, even different ports differentiate a domain.

```typescript
  useEffect(() => {
    async function fetchData() {
      const token = (await Auth.currentSession()).getIdToken().getJwtToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/tests`,
        {
          method: "POST",
          credentials: "include",
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ test_id: params.test }),
        },
      );
      const content = await response.json();
      setTestWithAssignedUsers(content.data);
      setAssignedUsers(content.data.assigned_users);
    }
    fetchData().catch((error) => {
      if (error.response?.status === 404) {
        router.push("/not-found");
      }
    });
  }, [params.test, router]);
```

## [Infrastructure-as-code - CDK](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-tenant-infra?region=eu-west-2)

Our infrastructure-as-code framework is [CDK](https://aws.amazon.com/cdk/) using TypeScript bindings. The Accenture AWS environment has already been [boostrapped to work with it](https://docs.aws.amazon.com/cdk/v2/guide/getting_started.html#getting_started_bootstrap).

In order to have a working local environment you need to have a `.env` file at the root of the project. I am pasting the real values that work with our current test client (clientc) here, but the generic schema is defined also [in the project's README](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-tenant-infra/README.md?region=eu-west-2):

```dotenv
CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=eu-west-2
CDK_DERP_TENANT_NAME=clientc
CDK_DERP_BASE_DOMAIN=zero-cool.co.uk
```

You also need to configure your AWS CLI using `aws configure` with keys (access key, secret) from a IAM user having CDK permissions. Feel free to replicate what I have done for [my IAM user](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/users/details/a.panagiotopoulos.cli?section=permissions) where I have created and assigned `cdk-deployer` as a policy. More details about the necessary prerequisites are in the [official CDK documentation](https://docs.aws.amazon.com/cdk/v2/guide/getting_started.html#getting_started_prerequisites).

Currently, this project contains two CDK stacks:

* `doge-to-derp-pipeline` which builds all components for the data pipeline between Doge and Derp
* `derp-{client-name}-infra-stack` where `client-name` is retrieved from an environment variable which builds all components for a specific tenant name

Adding or removing (or commenting out) blocks from each stack will be detected when invoking CDK and the differences will be deployed.

## What needs to be done

* Fix CORS issues between UI and API in AWS
* [The Lambda code](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/lambda-add-client-case-to-portal/lambda_function.py?region=eu-west-2)
* Stabilisation of features of UI and API, agree on a set of features as a MVP so we can add proper testing with coverage, the inclusion of phases in tests for example broke a lot of functionality
* Improve UI for smaller screens and tablets
* CI/CD but taking into account the multitenancy nature of the project. Maybe add workflows that deploy to a tenant based on a PR on dedicated branches, for example accepted PRs on branch `clientc` in Git, deploy changes to tenant `clientc`. PRs should trigger, for example for [`derp-api`](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/pyproject.toml?region=eu-west-2&lines=24-24), linting tests with [`ruff`](https://beta.ruff.rs/docs/), formatting tests with [`black`](https://black.readthedocs.io/en/stable/) and tests with [`pytest`](https://docs.pytest.org/en/7.4.x/).