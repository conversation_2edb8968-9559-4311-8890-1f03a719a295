# Derp UI

<!-- TOC -->

* [Derp UI](#derp-ui)
    * [Prerequisites](#prerequisites)
        * [1. Install Node.js 22 and pnpm](#1-install-nodejs-22-and-pnpm)
            * [MacOS (using brew)](#macos-using-brew)
            * [Linux](#linux)
        * [2. Install project dependencies](#2-install-project-dependencies)
        * [3. Create a
          `.env.local` file in the root of the project with the necessary variables](#3-create-a-envlocal-file-in-the-root-of-the-project-with-the-necessary-variables)
    * [Running](#running)
        * [Run a development server](#run-a-development-server)
    * [Docker](#docker)
        * [Push to AWS ECR (View push commands)](#push-to-aws-ecr-view-push-commands)
        * [Run container locally](#run-container-locally)

<!-- TOC -->

This is a [Next.js](https://nextjs.org/) project bootstrapped
with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Prerequisites

### 1. Install [Node.js 22](https://nodejs.org/en/download) and [pnpm](https://pnpm.io/installation)

#### MacOS (using [brew](https://brew.sh/))

```bash
brew install node@22 pnpm
```

#### Linux

```bash
# installs nvm (Node Version Manager)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.0/install.sh | bash
# download and install Node.js
nvm install 22
# download and install pnpm
curl -fsSL https://get.pnpm.io/install.sh | sh -
```

### 2. Install project dependencies

From the root of the project execute:

```bash
pnpm install
```

### 3. Create a `.env.local` file in the root of the project with the necessary variables

```dotenv
# Cognito
NEXT_PUBLIC_USER_POOL_ID=changeme
NEXT_PUBLIC_USER_POOL_WEB_CLIENT_ID=changeme

# Tenant name
NEXT_PUBLIC_CLIENT_NAME='Client A'

# This is the URL where your API runs, usually with a locally deployed FastAPI app, port 8000 is used
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
```

## Running

### Run a development server

From the root of the project run:

```bash
pnpm run dev
```

## Docker

[Next.js documentation about building a Docker image](https://nextjs.org/docs/pages/building-your-application/deploying#docker-image).

Docker images are pushed in AWS ECR in
the [derp-ui repository](https://eu-west-2.console.aws.amazon.com/ecr/repositories/private/************/derp-ui?region=eu-west-2).

Building a Docker image for a tenant requires
using [environment variables from a
`.env.local` file in the root directory](https://nextjs.org/docs/pages/building-your-application/configuring/environment-variables),
as those are needed during the build time of the app. Passing environment variables during `docker run` will not work.

### Push to AWS ECR ([View push commands](https://eu-west-2.console.aws.amazon.com/ecr/repositories/private/************/derp-ui?region=eu-west-2))

In order to create a dedicated Docker image for a tenant, we can tag it in the following commands with that tenant name
before pushing it to ECR.

1. Retrieve an authentication token and authenticate your Docker client to your registry. Use the AWS CLI:

    ```bash
    aws ecr get-login-password --region eu-west-2 | docker login --username AWS --password-stdin ************.dkr.ecr.eu-west-2.amazonaws.com
    ```

   Note: if you receive an error using the AWS CLI, make sure that you have the latest version of the AWS CLI and Docker
   installed.

2. Build your Docker image using the following command. For information on building a Docker file from scratch, see the
   instructions [here](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/create-container-image.html). You can
   skip this step if your image has already been built:

    ```bash
   docker build --platform=linux/amd64 -t derp-ui:tenantname .
   ```

3. After the build is completed, tag your image, so you can push the image to this repository.

   Warning! Use a Docker image tag that corresponds to the client name. It is the one that will be used by CDK to deploy
   the Docker image.

   [CDK snippet](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-tenant-infra/lib/derp-tenant-infra-stack.ts?region=eu-west-2&lines=346-346):
    ```typescript
    taskImageOptions: {
      image: ecs.ContainerImage.fromEcrRepository(
        derpUiImageRepository,
        tenant,
      )
      }
    ```

    ```bash
    docker tag derp-ui:tenantname ************.dkr.ecr.eu-west-2.amazonaws.com/derp-ui:tenantname
    ```

4. Run the following command to push this image to your newly created AWS repository:

    ```bash
    docker push ************.dkr.ecr.eu-west-2.amazonaws.com/derp-ui:tenantname
    ```

### Run container locally

```bash
docker run -d --rm -p 3000:3000 --name derp-ui derp-ui
```
