import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        accenture: {
          "core-purple-1": "#a100ff",
          "core-purple-2": "#7500c0",
          "core-purple-3": "#460073",
          "accent-purple-1": "#b455aa",
          "accent-purple-2": "#a055f5",
          "accent-purple-3": "#be82ff",
          "accent-purple-4": "#dcafff",
          "accent-purple-5": "#e6dcff",
          black: "#000000",
          "dark-gray": "#96968c",
          "light-gray": "#e6e6dc",
          white: "#ffffff",
          blue: "#0041f0",
          "light-blue": "#00ffff",
          green: "#64ff50",
          "blue-green": "#05f0a5",
          red: "#ff3246",
          pink: "#ff50a0",
          orange: "#ff7800",
          yellow: "#ffeb32",
        },
      },
      fontFamily: {
        "accenture-main": ["var(--font-accenture-main)"],
        "accenture-main-semi-bold": ["var(--font-accenture-main-semibold)"],
        "accenture-main-bold": ["var(--font-accenture-main-bold)"],
      },
    },
  },
  plugins: [require("@tailwindcss/typography")],
};
export default config;
