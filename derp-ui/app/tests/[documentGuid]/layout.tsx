"use client";

import { Test } from "@/app/tests/[documentGuid]/schemas";
import { Disclosure } from "@headlessui/react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/20/solid";
import { fetchAuthSession } from "aws-amplify/auth";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

export default function TestLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const testId = pathname.split("/")[2];
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(true);
  const [test, setTest] = useState<Test | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const authToken = (
          await fetchAuthSession()
        ).tokens?.accessToken?.toString();

        const response = await fetch(`/api/tests/${testId}`, {
          credentials: "include",
          headers: {
            Authorization: `Bearer ${authToken}`,
            Accept: "application/json",
          },
        });
        if (!response.ok) {
          router.push("/not-found");
        } else {
          const result: Test = await response.json();
          setTest(result);
        }
      } catch (error) {
        router.push("/not-found");
      }
    };
    fetchData();
  }, [router, testId]);

  const toggleDrawer = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="h-full lg:flex lg:h-full lg:flex-row">
      <div
        className={`${isOpen ? "translate-x-0" : "hidden -translate-x-full"} z-1 fixed transform bg-black bg-opacity-10 shadow-md transition-transform duration-300 lg:${
          isOpen ? "visible w-64" : "hidden w-0"
        } lg:transition-width fixed lg:relative lg:z-0 lg:flex-shrink-0 lg:overflow-x-hidden lg:bg-neutral-50 lg:duration-300 lg:ease-in-out`}
      >
        <div className="border border-zinc-100 bg-neutral-50 lg:flex-grow lg:overflow-y-hidden">
          <div className="flex h-[calc(100vh-8rem)] flex-col space-y-8 overflow-y-scroll p-6 lg:overflow-y-hidden">
            <div>
              <div className="py-2 font-accenture-main-semi-bold capitalize">
                Web Application Test
              </div>
              <div>
                <span>Test ID:</span>
                <span>&nbsp;</span>
                <span className="font-accenture-main-semi-bold text-purple-600">
                  {test?.engagement_number}
                </span>
              </div>
            </div>
            <div>
              <div className="py-2 font-accenture-main-semi-bold uppercase">
                Findings
              </div>
              <div className="space-y-6">
                {test?.phases.map((phase) => (
                  <div key={phase.number}>
                    <div className="text-m font-accenture-main-semi-bold">
                      {test?.engagement_number}-{phase.number}: {phase.name}
                    </div>
                    <div className="flex flex-col items-start">
                      {phase.findings.map((finding) => (
                        <Disclosure key={finding.number}>
                          {({ open }) => (
                            <>
                              <Disclosure.Button
                                className={
                                  open
                                    ? "border-l-4 border-l-accenture-core-purple-1 bg-accenture-accent-purple-5 px-2 py-2 text-left"
                                    : "px-2 py-2 text-left"
                                }
                              >
                                <span className="font-accenture-main-semi-bold text-purple-600">
                                  {test.engagement_number}-{phase.number}-
                                  {String(finding.number).padStart(2, "0")}
                                </span>
                                <span>&#58;</span>
                                <span>&nbsp;</span>
                                <span>{finding.name}</span>
                              </Disclosure.Button>
                              <Disclosure.Panel className="ml-4 space-y-4 py-2">
                                {finding.has_description ? (
                                  <div>
                                    <Link
                                      href={`/tests/${test.document_guid}/phases/${phase.number}/findings/${finding.number}#description`}
                                      prefetch={false}
                                    >
                                      Description
                                    </Link>
                                  </div>
                                ) : (
                                  <></>
                                )}
                                {finding.has_recommendation ? (
                                  <div>
                                    <Link
                                      href={`/tests/${test.document_guid}/phases/${phase.number}/findings/${finding.number}#recommendation`}
                                      prefetch={false}
                                    >
                                      Recommendation
                                    </Link>
                                  </div>
                                ) : (
                                  <></>
                                )}
                                {finding.has_associated_hosts ? (
                                  <div>
                                    <Link
                                      href={`/tests/${test.document_guid}/phases/${phase.number}/findings/${finding.number}#associated-hosts`}
                                      prefetch={false}
                                    >
                                      Associated Hosts
                                    </Link>
                                  </div>
                                ) : (
                                  <></>
                                )}
                                {finding.has_supporting_material ? (
                                  <div>
                                    <Link
                                      href={`/tests/${test.document_guid}/phases/${phase.number}/findings/${finding.number}#supporting-material`}
                                      prefetch={false}
                                    >
                                      Supporting Material
                                    </Link>
                                  </div>
                                ) : (
                                  <></>
                                )}
                              </Disclosure.Panel>
                            </>
                          )}
                        </Disclosure>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      <button
        onClick={toggleDrawer}
        className={`z-2 top-50 fixed h-12 w-12 ${isOpen ? "translate-x-[14.5rem]" : "-translate-x-4"} translate-y-10 rounded-full border border-zinc-100 bg-white text-accenture-core-purple-1 lg:relative lg:-translate-x-5`}
      >
        {isOpen ? (
          <ChevronLeftIcon className="ml-1.5 h-8 w-8" aria-hidden="false" />
        ) : (
          <ChevronRightIcon className="ml-2 h-8 w-8" aria-hidden="true" />
        )}
      </button>
      {children}
    </div>
  );
}
