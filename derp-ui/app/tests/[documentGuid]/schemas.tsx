export type AssociatedHost = {
  ip_address: string;
  network: string;
  port: number;
  protocol: string;
};

export type Finding = {
  number: number;
  engagement_number: number;
  phase_number: number;
  name: string;
  status: string;
  scoring: Scoring;
  description?: string;
  recommendation?: string;
  supporting_material?: string;
  associated_hosts: AssociatedHost[];
};

export type Scoring = {
  metric_groups: MetricGroup[];
  base_severity: string;
  vector_string: string;
};

export type MetricGroup = {
  title: string;
  metrics: Metric[];
};

export type Metric = {
  attribute: string;
  value: string;
};

export type TestFinding = {
  number: number;
  name: string;
  base_severity: string;
  status: string;
  has_description: boolean;
  has_recommendation: boolean;
  has_supporting_material: boolean;
  has_associated_hosts: boolean;
};

export type Phase = {
  number: number;
  name: string;
  findings: TestFinding[];
};

export type Test = {
  document_guid: string;
  engagement_number: string;
  name: string;
  phases: Phase[];
};
