"use client";

import { MetricGroup } from "@/app/tests/[documentGuid]/schemas";
import { Disclosure } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";

type Props = {
  metricGroups: MetricGroup[];
};

export default function MetricGroups({ metricGroups }: Props) {
  return (
    <div className="prose">
      <div className="mx-auto w-full space-y-1 rounded-2xl bg-white p-2">
        {metricGroups.map((metricGroup) => (
          <Disclosure key={metricGroup.title}>
            {({ open }) => (
              <>
                <Disclosure.Button className="wrap flex w-full justify-between rounded-lg bg-purple-100 px-4 py-2 text-left text-purple-900 hover:bg-purple-200 focus:outline-none focus-visible:ring focus-visible:ring-purple-500/75">
                  <span>{metricGroup.title.toUpperCase()}</span>
                  <ChevronDownIcon
                    className={`${
                      open ? "rotate-180 transform" : ""
                    } h-5 w-5 text-purple-500`}
                  />
                </Disclosure.Button>
                <Disclosure.Panel className="flex flex-row flex-wrap px-4 pb-2 pt-4">
                  {metricGroup.metrics.map((metric) => (
                    <div
                      className="m-1 flex flex-row items-center space-x-2 rounded-xl bg-gray-200 p-2"
                      key={metric.attribute}
                    >
                      <div>{metric.attribute}:</div>
                      <div
                        className={`detail-rating-${metric.value} p-1 text-sm uppercase`}
                      >
                        {metric.value}
                      </div>
                    </div>
                  ))}
                </Disclosure.Panel>
              </>
            )}
          </Disclosure>
        ))}
      </div>
    </div>
  );
}
