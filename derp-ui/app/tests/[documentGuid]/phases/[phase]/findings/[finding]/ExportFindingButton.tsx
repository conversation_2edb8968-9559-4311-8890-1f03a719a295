"use client";

import { downloadBlob } from "@/app/utils/downloader";
import { fetchAuthSession } from "aws-amplify/auth";
import React from "react";

type Props = {
  findingNumber: number | undefined;
};

export default function ExportFindingButton({ findingNumber }: Props) {
  async function fetchData() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    try {
      const response = await fetch(
        `/api/finding/${findingNumber}?export=true`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        },
      );
      downloadBlob(await response.blob(), `finding-${findingNumber}.csv`);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  }

  return (
    <button
      onClick={fetchData}
      className="space-x-2 rounded-full bg-purple-600 px-4 py-2 font-accenture-main-semi-bold text-white"
    >
      <span>&#43;</span>
      <span>Export</span>
    </button>
  );
}
