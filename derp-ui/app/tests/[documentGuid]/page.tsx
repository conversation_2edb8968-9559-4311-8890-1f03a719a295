import FindingStatus from "@/app/components/FindingStatus";
import ExportFindingsButton from "@/app/tests/[documentGuid]/ExportFindingsButton";
import { Phase, Test } from "@/app/tests/[documentGuid]/schemas";
import { runWithAmplifyServerContext } from "@/app/utils/amplifyServerUtils";
import { fetchAuthSession } from "aws-amplify/auth/server";
import { Metadata } from "next";
import { cookies } from "next/headers";
import Link from "next/link";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Customer Portal",
};
export const dynamic = "force-dynamic";

async function getTest(documentGuid: string): Promise<Test> {
  const authSession = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: (contextSpec) => fetchAuthSession(contextSpec),
  });

  const authToken = authSession.tokens?.accessToken?.toString();
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/tests/${documentGuid}`,
    {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${authToken}`,
        Accept: "application/json",
      },
    },
  );

  if (!response.ok) {
    redirect("/not-found");
  }
  return response.json();
}

export default async function TestPage({
  params,
}: {
  params: { documentGuid: string };
}) {
  try {
    const test: Test = await getTest(params.documentGuid);

    return (
      <div className="flex h-full w-full flex-col px-6 py-4">
        <nav className="flex py-3" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-2 md:space-x-3">
            <li className="inline-flex items-center text-neutral-600">
              <Link href={`/dashboard`} prefetch={false}>
                Dashboard
              </Link>
            </li>
            <li>&gt;</li>
            <li>
              <div className="flex items-center font-accenture-main-semi-bold text-black">
                <Link href={`/tests/${test?.document_guid}`} prefetch={false}>
                  Test Detail
                </Link>
              </div>
            </li>
          </ol>
        </nav>
        <div className="my-4 flex flex-col items-start space-y-6 md:flex-row md:items-center md:space-x-28 md:space-y-0">
          <div className="font-accenture-main-bold text-2xl md:text-3xl">
            Summary of Findings
          </div>
          <ExportFindingsButton
            testDocumentGuid={test?.document_guid}
            testEngagementNumber={test?.engagement_number}
          />
        </div>
        {test?.phases.map((phase: Phase) => (
          <div className="flex flex-col py-6" key={phase.number}>
            <div className="font-accenture-main-semi-bold text-xl">
              {test.engagement_number}-{phase.number}: {phase.name}
            </div>
            <div className="overflow-x-auto">
              <table className="mt-4 table-auto font-accenture-main">
                <thead className="border-b-2 border-neutral-200 font-accenture-main-semi-bold">
                  <tr>
                    <th className="min-w-28">Finding ID</th>
                    <th className="min-w-28">Finding Name</th>
                    <th className="min-w-28">Rating</th>
                    <th className="min-w-28">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {phase.findings.map((finding) => (
                    <tr
                      className="text-center odd:bg-white even:bg-neutral-50"
                      key={finding.number}
                    >
                      <td className="text-medium py-4 font-accenture-main-semi-bold text-purple-600">
                        <Link
                          href={`/tests/${test.document_guid}/phases/${phase.number}/findings/${finding.number}`}
                          prefetch={false}
                        >
                          {test.engagement_number}-{phase.number}-
                          {String(finding.number).padStart(2, "0")}
                        </Link>
                      </td>
                      <td>{finding.name}</td>
                      <td>
                        <span
                          className={`base-severity-${finding.base_severity} p-1 font-accenture-main-semi-bold text-sm uppercase text-black`}
                        >
                          {finding.base_severity}
                        </span>
                      </td>
                      <td>
                        <FindingStatus status={finding?.status} />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}

        {/*{findings.length === 0 && (*/}
        {/*  <div className="container mx-auto flex w-full items-center justify-center bg-neutral-50 py-28">*/}
        {/*    <div className="text-zinc-700">There are no records to display.</div>*/}
        {/*  </div>*/}
        {/*)}*/}
      </div>
    );
  } catch (error) {
    return <p>Something went wrong...</p>;
  }
}
