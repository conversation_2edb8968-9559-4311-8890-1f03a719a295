"use client";

import { components, formFields } from "@/app/components/Auth";
import ConfigureAmplifyClientSide from "@/app/components/ConfigureAmplifyClientSide";
import Footer from "@/app/components/Footer";
import Header from "@/app/components/Header";
import { Authenticator } from "@aws-amplify/ui-react";
import "@aws-amplify/ui-react/styles.css";
import { fetchAuthSession } from "aws-amplify/auth";
import localFont from "next/font/local";
import React, { useState } from "react";
import useSWR, { Fetcher } from "swr";

import SideNav from "./components/SideNav";
import "./globals.css";

const accentureMainFont = localFont({
  src: "../public/fonts/Graphik-Regular.ttf",
  display: "swap",
  variable: "--font-accenture-main",
});

const accentureMainSemiboldFont = localFont({
  src: "../public/fonts/Graphik-Semibold.ttf",
  display: "swap",
  variable: "--font-accenture-main-semibold",
});

const accentureMainBoldFont = localFont({
  src: "../public/fonts/Graphik-Bold.ttf",
  display: "swap",
  variable: "--font-accenture-main-bold",
});

interface Profile {
  given_name: string;
  family_name: string;
  email: string;
  groups: string[];
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  const fetcher: Fetcher<Profile> = async (url: string) => {
    const authToken = (await fetchAuthSession()).tokens?.idToken?.toString();
    const response = await fetch(url, {
      credentials: "include",
      headers: {
        Authorization: `Bearer ${authToken}`,
        Accept: "application/json",
      },
      cache: "no-store",
    });

    return response.json();
  };
  const { data, error } = useSWR("/api/users/profile", fetcher);

  return (
    <html
      lang="en"
      className={`${accentureMainFont.variable} ${accentureMainSemiboldFont.variable} ${accentureMainBoldFont.variable}`}
    >
      <body>
        <>
          <ConfigureAmplifyClientSide />
          <Authenticator formFields={formFields} components={components}>
            {({ signOut, user }) => (
              <div className="flex min-h-screen w-full flex-col">
                <Header
                  signOut={signOut}
                  toggleSideNav={toggleSideNav}
                  data={data}
                />
                <SideNav
                  isSideNavOpen={isSideNavOpen}
                  toggleSideNav={toggleSideNav}
                  data={data}
                />
                <div className="flex-grow font-accenture-main">{children}</div>
                <Footer />
              </div>
            )}
          </Authenticator>
        </>
      </body>
    </html>
  );
}
