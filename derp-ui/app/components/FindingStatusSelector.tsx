"use client";

import FindingStatus from "@/app/components/FindingStatus";
import { Menu } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import { fetchAuthSession } from "aws-amplify/auth";
import { useState } from "react";

type Props = {
  documentGuid: string;
  phase: string;
  finding: number;
  status: string;
};
export const dynamic = "force-dynamic";

export default function FindingStatusSelector({
  documentGuid,
  phase,
  finding,
  status,
}: Props) {
  const [newStatus, setNewStatus] = useState(status);
  const [error, setError] = useState("");

  async function setFindingStatus(
    documentGuid: string,
    phase: string,
    finding: number,
    status: string,
  ): Promise<void> {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();

    try {
      const response = await fetch(
        `/api/tests/${documentGuid}/phases/${phase}/findings/${finding}/status`,
        {
          method: "PUT",
          credentials: "include",
          cache: "no-store",
          headers: {
            Authorization: `Bearer ${authToken}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            finding_status: status,
          }),
        },
      );

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      setNewStatus(status);
    } catch (e) {
      setError("Error setting finding status");
    }
  }

  return (
    <div className="flex flex-row items-center space-x-3">
      <Menu>
        <Menu.Button>
          <div className="flex flex-row items-center">
            <FindingStatus status={newStatus} />
            <ChevronDownIcon
              className="-mr-1 ml-2 h-5 w-5 text-gray-500"
              aria-hidden="true"
            />
          </div>
        </Menu.Button>
        <Menu.Items className="absolute w-40 origin-top-right bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="px-2 py-2">
            <Menu.Item>
              {({ active }) => (
                <div
                  onClick={() =>
                    setFindingStatus(documentGuid, phase, finding, "OPEN")
                  }
                  className="cursor-pointer hover:bg-neutral-200"
                >
                  <FindingStatus status="OPEN" />
                </div>
              )}
            </Menu.Item>
          </div>
          <div className="px-2 py-2">
            <Menu.Item>
              {({ active }) => (
                <div
                  onClick={() =>
                    setFindingStatus(documentGuid, phase, finding, "CLOSED")
                  }
                  className="cursor-pointer hover:bg-neutral-200"
                >
                  <FindingStatus status="CLOSED" />
                </div>
              )}
            </Menu.Item>
          </div>
          <div className="px-2 py-2">
            <Menu.Item>
              {({ active }) => (
                <div
                  onClick={() =>
                    setFindingStatus(
                      documentGuid,
                      phase,
                      finding,
                      "FALSE_POSITIVE",
                    )
                  }
                  className="cursor-pointer hover:bg-neutral-200"
                >
                  <FindingStatus status="FALSE_POSITIVE" />
                </div>
              )}
            </Menu.Item>
          </div>
        </Menu.Items>
      </Menu>
      <div className="text-red-400">{error}</div>
    </div>
  );
}
