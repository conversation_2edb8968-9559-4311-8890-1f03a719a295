import {
  Button,
  Heading,
  Text,
  View,
  useAuthenticator,
  useTheme,
} from "@aws-amplify/ui-react";
import Image from "next/image";

export const components = {
  Header() {
    const { tokens } = useTheme();

    return (
      <View className="flex items-baseline justify-center space-x-8 divide-x py-8 md:grid md:grid-cols-2 md:space-x-0">
        <Image
          alt="Accenture logo"
          src="/images/Acc_Logo_Black_Purple_RGB.svg"
          width="100"
          height="100"
        />
        <div className="pl-8 text-right font-accenture-main-bold md:pl-0">
          Accenture Security
        </div>
      </View>
    );
  },

  Footer() {
    const { tokens } = useTheme();

    return (
      <View textAlign="center" padding={tokens.space.large}>
        <Text
          color={tokens.colors.neutral[80]}
          className="text-xs md:text-base"
        >
          &copy; All Rights Reserved
        </Text>
      </View>
    );
  },

  SignIn: {
    Header() {
      const { tokens } = useTheme();

      return (
        <Heading
          padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`}
          level={3}
          className="text-xl md:text-3xl"
        >
          Sign in to your account
        </Heading>
      );
    },

    Footer() {
      const { toForgotPassword } = useAuthenticator();

      return (
        <View textAlign="center">
          <Button
            fontWeight="normal"
            onClick={toForgotPassword}
            size="small"
            variation="link"
          >
            Reset Password
          </Button>
        </View>
      );
    },
  },

  SignUp: {
    Header() {
      const { tokens } = useTheme();

      return (
        <Heading
          padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`}
          level={3}
        >
          Create a new account
        </Heading>
      );
    },
    Footer() {
      const { toSignIn } = useAuthenticator();

      return (
        <View textAlign="center">
          <Button
            fontWeight="normal"
            onClick={toSignIn}
            size="small"
            variation="link"
          >
            Back to Sign In
          </Button>
        </View>
      );
    },
  },
  ConfirmSignUp: {
    Header() {
      const { tokens } = useTheme();
      return (
        <Heading
          padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`}
          level={3}
          className="text-xl md:text-3xl"
        >
          Enter Information:
        </Heading>
      );
    },
    Footer() {
      return <Text></Text>;
    },
  },
  SetupTotp: {
    Header() {
      const { tokens } = useTheme();
      return (
        <Heading
          padding={`${tokens.space.xl} 0 0 ${tokens.space.xl}`}
          level={3}
        >
          <div className="space-y-4">
            <div>Enable MFA for Login:</div>
            <div className="mt-3 text-base">
              Within your chosen authenticator app scan the QR code below to add
              an account for the portal to enable MFA login.
            </div>
            {/*<Accordion.Container>*/}
            {/*  <Accordion.Item*/}
            {/*    className="bg-purple-100 font-accenture-main text-sm text-purple-900"*/}
            {/*    value="Accordion-item"*/}
            {/*  >*/}
            {/*    <Accordion.Trigger>*/}
            {/*      Add new account in Microsoft Authenticator*/}
            {/*      <Accordion.Icon />*/}
            {/*    </Accordion.Trigger>*/}
            {/*    <Accordion.Content>*/}
            {/*      <Image*/}
            {/*        alt="Add Microsoft Authenticator account illustration"*/}
            {/*        src="/images/Add_MS_Auth_Account_Illustration.jpg"*/}
            {/*        width="500"*/}
            {/*        height="500"*/}
            {/*      />*/}
            {/*    </Accordion.Content>*/}
            {/*  </Accordion.Item>*/}
            {/*</Accordion.Container>*/}
          </div>
        </Heading>
      );
    },
    Footer() {
      return <Text></Text>;
    },
  },
  ConfirmSignIn: {
    Header() {
      const { tokens } = useTheme();
      return (
        <Heading level={3} className="text-xl md:text-3xl">
          Enter MFA Code:
        </Heading>
      );
    },
    Footer() {
      return <Text></Text>;
    },
  },
  ResetPassword: {
    Header() {
      const { tokens } = useTheme();
      return (
        <Heading level={3} className="text-xl md:text-3xl">
          Enter Information:
        </Heading>
      );
    },
    Footer() {
      return <Text></Text>;
    },
  },
  ConfirmResetPassword: {
    Header() {
      const { tokens } = useTheme();
      return (
        <Heading level={3} className="text-xl md:text-3xl">
          Enter Information:
        </Heading>
      );
    },
    Footer() {
      return <Text></Text>;
    },
  },
};

export const formFields = {
  signIn: {
    username: {
      label: "Email address",
      placeholder: "Enter email",
    },
    password: {
      label: "Password",
      placeholder: "Enter password",
    },
  },
  signUp: {
    password: {
      label: "Password",
      placeholder: "Enter Password:",
      isRequired: false,
      order: 2,
    },
    confirm_password: {
      label: "Confirm Password:",
      order: 1,
    },
  },
  forceNewPassword: {
    password: {
      placeholder: "Enter Password:",
    },
  },
  resetPassword: {
    username: {
      placeholder: "Enter Email:",
    },
  },
  confirmResetPassword: {
    confirmation_code: {
      placeholder: "Enter your Confirmation Code:",
      label: "",
      isRequired: false,
    },
    confirm_password: {
      placeholder: "Enter your Password Please:",
    },
  },
  setupTotp: {
    QR: {
      totpIssuer: `Accenture Doge Enterprise Portal | ${process.env.NEXT_PUBLIC_CLIENT_NAME}`,
      totpUsername: "",
    },
    confirmation_code: {
      label: "",
      placeholder: "Enter your Confirmation Code:",
      isRequired: false,
    },
  },
  confirmSignIn: {
    confirmation_code: {
      label:
        "Please type in the MFA code displayed in your chosen authenticator app.",
      placeholder: "Enter your code:",
      isRequired: false,
    },
  },
};
