@tailwind base;
@tailwind components;
@tailwind utilities;

.base-severity-critical,
.detail-rating-critical {
    background-color: #ffadb5;
}

.base-severity-high,
.detail-rating-high {
    background-color: #ffc999;
}

.base-severity-medium,
.detail-rating-medium {
    background-color: #fffbd6;
}

.base-severity-low,
.detail-rating-low {
    background-color: #c1ffb9;
}

.base-severity-minimal,
.base-severity-very_low,
.detail-rating-minimal,
.detail-rating-very_low {
    background-color: #99b3f9;
}

span.highlighted {
    background-color: #ffff00ff;
    color: #c42026ff;
}

.amplify-button[data-variation="primary"] {
    background: #a100ff;
}

.amplify-tabs__list {
    display: none;
}

button.amplify-button[type="submit"] {
    background-color: #a100ff;
}

.amplify-field-group__control {
    outline: none;
}

.amplify-field-group__control:focus {
    border-color: #a100ff;
}

.amplify-field-group__control:hover {
    border-color: #a100ff;
}

@font-face {
    font-family: "Graphik";
    src: url("/fonts/Graphik-Regular.ttf");
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: "Graphik";
    src: url("/fonts/Graphik-Bold.ttf");
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: "Graphik";
    src: url("/fonts/Graphik-Light.ttf");
    font-weight: light;
    font-style: normal;
}

.amplify-button,
.amplify-heading,
.amplify-label {
    font-family: "Graphik";
    font-weight: bold;
}

.amplify-text,
.amplify-input {
    font-family: "Graphik";
    font-weight: normal;
}
