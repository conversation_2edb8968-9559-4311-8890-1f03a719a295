"use client";

import { ArchiveBoxArrowDownIcon } from "@heroicons/react/20/solid";
import { fetchAuthSession } from "aws-amplify/auth";
import Link from "next/link";
import { useState } from "react";

type Props = {
  tests: Test[];
};

export default function UnarchivedTestsTable({ tests }: Props) {
  const [testRows, setTestRows] = useState(tests);

  async function archiveTest(documentGuid: string) {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    try {
      const response = await fetch(`/api/tests/${documentGuid}/archive`, {
        method: "PUT",
        credentials: "include",
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });

      if (!response.ok) {
        console.error("Error");
      } else {
        const filteredTests = testRows.filter(
          (testRow) => testRow.document_guid !== documentGuid,
        );
        setTestRows(filteredTests);
      }
    } catch (error) {
      console.error("Error");
    }
  }

  return (
    <div className="flex flex-col overflow-x-auto">
      <table className="grow table-auto">
        <thead className="border-b-2 border-neutral-200 font-accenture-main-semi-bold">
          <tr>
            <th className="min-w-28">Test ID</th>
            <th className="min-w-28">Test Name</th>
            <th className="min-w-28">Overall Findings</th>
            <th className="min-w-28">Critical Findings</th>
            <th className="min-w-28">High Findings</th>
            <th className="min-w-28">Medium Findings</th>
            <th className="min-w-28">Low Findings</th>
            <th className="min-w-28">Minimal Findings</th>
            <th className="min-w-28">Time of Last Export</th>
            <th className="min-w-28">Archive</th>
          </tr>
        </thead>
        <tbody>
          {testRows.map((item) => (
            <tr
              className="text-center odd:bg-white even:bg-neutral-50"
              key={item.document_guid}
            >
              <td className="text-medium py-4 font-accenture-main-semi-bold text-purple-600">
                <Link href={`/tests/${item.document_guid}`} prefetch={false}>
                  {item.engagement_number}
                </Link>
              </td>
              <td className="min-w-28">{item.name}</td>
              <td className="min-w-28">{item.findings.total}</td>
              <td className="min-w-28">{item.findings.critical}</td>
              <td className="min-w-28">{item.findings.high}</td>
              <td className="min-w-28">{item.findings.medium}</td>
              <td className="min-w-28">{item.findings.low}</td>
              <td className="min-w-28">{item.findings.minimal}</td>
              <td className="min-w-28">{item.time_of_last_export}</td>
              <td className="min-w-28">
                <div className="flex justify-center">
                  <ArchiveBoxArrowDownIcon
                    onClick={() => archiveTest(item.document_guid)}
                    className="h-6 w-6 cursor-pointer"
                  />
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {tests.length === 0 && (
        <div className="container mx-auto flex w-full items-center justify-center bg-neutral-50 py-28">
          <div className="text-zinc-700">There are no records to display.</div>
        </div>
      )}
    </div>
  );
}
