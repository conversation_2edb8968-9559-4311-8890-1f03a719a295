import Banner from "@/app/components/Banner";
import ExportTestsButton from "@/app/dashboard/ExportTestsButton";
import React from "react";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="p-8">
      <Banner />
      <div className="flex flex-col items-center justify-between space-y-6 py-8 md:flex-row md:space-y-0">
        <div className="flex self-start font-accenture-main-bold text-3xl">
          Dashboard
        </div>
        <ExportTestsButton />
      </div>
      <div>{children}</div>
    </div>
  );
}
