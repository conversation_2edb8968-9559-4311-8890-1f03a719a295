import Link from "next/link";
import React from "react";

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="p-8 md:px-10 xl:px-60">
      <nav className="flex py-4" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-2 md:space-x-3">
          <li className="inline-flex items-center text-neutral-600">
            <Link href={`/dashboard`} prefetch={false}>
              Dashboard
            </Link>
          </li>
          <li>&gt;</li>
          <li>
            <div className="flex items-center font-accenture-main-semi-bold text-black">
              <Link href={`/profile`} prefetch={false}>
                My Profile
              </Link>
            </div>
          </li>
        </ol>
      </nav>
      <div>{children}</div>
    </div>
  );
}
