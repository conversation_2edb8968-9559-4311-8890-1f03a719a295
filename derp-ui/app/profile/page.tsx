import ArchivedTestsTable from "@/app/profile/ArchivedTestsTable";
import { runWithAmplifyServerContext } from "@/app/utils/amplifyServerUtils";
import { fetchAuthSession } from "aws-amplify/auth/server";
import { Metadata } from "next";
import { cookies } from "next/headers";
import Link from "next/link";
import { notFound } from "next/navigation";

interface Profile {
  given_name: string;
  family_name: string;
  email: string;
}

export const metadata: Metadata = {
  title: "Customer Portal",
};
export const dynamic = "force-dynamic";

async function getProfile() {
  const authSession = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: (contextSpec) => fetchAuthSession(contextSpec),
  });
  const authToken = authSession.tokens?.idToken?.toString();
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/users/profile`,
    {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${authToken}`,
        Accept: "application/json",
      },
    },
  );
  return res.json();
}

async function getArchivedTests(): Promise<Test[]> {
  const authSession = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: (contextSpec) => fetchAuthSession(contextSpec),
  });
  const authToken = authSession.tokens?.accessToken?.toString();
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/tests?archived=true`,
    {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${authToken}`,
        Accept: "application/json",
      },
    },
  );

  if (!res.ok) {
    notFound();
  }
  const { tests } = await res.json();
  return tests;
}

export default async function ProfilePage() {
  try {
    const profile: Profile = await getProfile();
    const tests: Test[] = await getArchivedTests();
    return (
      <div className="flex flex-col space-y-6 py-4 md:space-y-10">
        <div className="flex flex-row items-center justify-between">
          <div className="font-accenture-main-bold text-2xl md:text-3xl">
            My Profile
          </div>
        </div>
        <div className="grid grid-cols-1 divide-y">
          <div className="flex flex-row justify-between space-x-6 py-8">
            <div className="font-accenture-main-semi-bold">First Name</div>
            <div>{profile?.given_name}</div>
          </div>
          <div className="flex flex-row justify-between space-x-6 py-8">
            <div className="font-accenture-main-semi-bold">Last Name</div>
            <div>{profile?.family_name}</div>
          </div>
          <div className="flex flex-row justify-between space-x-6 py-8">
            <div className="font-accenture-main-semi-bold">Email</div>
            <div>{profile?.email}</div>
          </div>
        </div>
        <div className="flex flex-row items-center justify-between">
          <div className="font-accenture-main-bold text-3xl">
            Archived Tests
          </div>
        </div>
        <div>
          <ArchivedTestsTable tests={tests} />
        </div>
      </div>
    );
  } catch (error) {
    return (
      <div className="flex flex-1">
        <p>Something went wrong...</p>
      </div>
    );
  }
}
