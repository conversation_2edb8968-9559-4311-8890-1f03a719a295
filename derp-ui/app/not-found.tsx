import { ArrowLeftIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import React from "react";

export const metadata = {
  title: "Customer Portal | Not found",
};

export default function NotFound() {
  return (
    <div className="mt-24 flex justify-center">
      <div className="flex flex-col space-y-4 px-10">
        <div>
          <Link
            href="/"
            className="flex flex-row items-center justify-start font-accenture-main-semi-bold"
          >
            <ArrowLeftIcon className="h-4 w-4 pr-2" />
            Go back
          </Link>
        </div>
        <div className="font-accenture-main-bold text-4xl font-bold">
          Oops! We can<span>&apos;</span>t find this page
        </div>
        <div>
          Sorry, the page you’re looking for doesn<span>&apos;</span>t exist.
        </div>
      </div>
    </div>
  );
}
