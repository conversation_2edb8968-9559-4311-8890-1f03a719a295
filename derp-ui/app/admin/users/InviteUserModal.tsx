"use client";

import InviteErrorMessage from "@/app/admin/users/InviteErrorMessage";
import InviteSuccessMessage from "@/app/admin/users/InviteSuccessMessage";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { fetchAuthSession } from "aws-amplify/auth";
import React, { Fragment, useRef, useState } from "react";

type Props = {
  retrieveUsers(): void;
};

export default function InviteUserModal({ retrieveUsers }: Props) {
  function closeModal() {
    setIsOpen(false);
    setNewUserEmail("");
    setShowErrorMessage(false);
    setShowSuccessMessage(false);
  }

  function openModal() {
    setIsOpen(true);
    setNewUserEmail("");
    setShowErrorMessage(false);
    setShowSuccessMessage(false);
  }

  const [isOpen, setIsOpen] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState("");
  const newUserInputEmailRef = useRef<HTMLInputElement>(null);
  const [errorMessage, setErrorMessage] = useState("");
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  async function inviteUser(userEmail: string) {
    const isEmailValid = newUserInputEmailRef.current?.validity.valid;

    if (!isEmailValid) {
      setErrorMessage("Invalid email");
      setShowErrorMessage(true);
      setShowSuccessMessage(false);
    } else {
      const authToken = (
        await fetchAuthSession()
      ).tokens?.accessToken?.toString();
      const res = await fetch("/api/admin/users/invite", {
        method: "POST",
        credentials: "include",
        headers: {
          Authorization: `Bearer ${authToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: decodeURIComponent(userEmail),
        }),
      });

      if (!res.ok) {
        setShowErrorMessage(true);
        setShowSuccessMessage(false);
        if (res.status === 400) {
          const error_message = await res.json();
          setErrorMessage(error_message["detail"]);
        } else {
          setErrorMessage("Invite failed, please contact a Portal Admin");
        }
      } else {
        setShowSuccessMessage(true);
        setShowErrorMessage(false);
        retrieveUsers();
      }
    }
  }
  return (
    <>
      <button
        onClick={openModal}
        className="w-full space-x-2 rounded-full bg-purple-600 px-4 py-2 font-accenture-main-semi-bold text-white md:w-auto"
      >
        <span>&#43;</span>
        <span>Invite user</span>
      </button>
      <>
        <Transition appear show={isOpen} as={Fragment}>
          <Dialog as="div" className="relative z-10" onClose={closeModal}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black/25" />
            </Transition.Child>

            <div className="fixed inset-0 overflow-y-auto">
              <div className="flex min-h-full items-center justify-center p-4 text-center">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 scale-95"
                  enterTo="opacity-100 scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 scale-100"
                  leaveTo="opacity-0 scale-95"
                >
                  <Dialog.Panel className="w-full max-w-md transform space-y-4 overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <div className="flex flex-row justify-between">
                        <div>Invite a new user</div>
                        <XMarkIcon
                          onClick={closeModal}
                          className="h-6 w-6 cursor-pointer"
                        />
                      </div>
                    </Dialog.Title>
                    <div className="flex flex-col">
                      <label
                        className="font-accenture-main-semi-bold"
                        htmlFor="new-user-email-input"
                      >
                        Email
                      </label>
                      <div className="flex flex-col justify-end space-x-4 space-y-6 md:flex-row md:space-y-0">
                        <input
                          ref={newUserInputEmailRef}
                          className="border-2 border-gray-300 px-2 outline-none hover:border-purple-700 focus:border-purple-700 md:w-96"
                          type="email"
                          value={newUserEmail}
                          onChange={(e) => setNewUserEmail(e.target.value)}
                          id="new-user-email-input"
                          required
                        />
                        <button
                          className="w-52 rounded-full bg-purple-600 px-4 py-2 font-accenture-main-semi-bold text-white"
                          onClick={() => inviteUser(newUserEmail)}
                        >
                          Send Invite
                        </button>
                      </div>
                    </div>
                    {showErrorMessage && (
                      <InviteErrorMessage message={errorMessage} />
                    )}
                    {showSuccessMessage && (
                      <InviteSuccessMessage
                        message={"Email invite sent successfully"}
                      />
                    )}
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>
      </>
    </>
  );
}
