"use client";

import InviteUserModal from "@/app/admin/users/InviteUserModal";
import { CheckCircleIcon, XCircleIcon } from "@heroicons/react/20/solid";
import { fetchAuthSession } from "aws-amplify/auth";
import { router } from "next/client";
import Link from "next/link";
import { useState } from "react";

type Props = {
  retrievedUsers: User[];
};
export default function UsersTable({ retrievedUsers }: Props) {
  const [users, setUsers] = useState<User[]>(retrievedUsers);

  async function retrieveUsers() {
    try {
      const authToken = (
        await fetchAuthSession()
      ).tokens?.accessToken?.toString();

      const response = await fetch(`/api/admin/users`, {
        credentials: "include",
        headers: {
          Authorization: `Bearer ${authToken}`,
          Accept: "application/json",
        },
        cache: "no-store",
      });
      if (!response.ok) {
        router.push("/not-found");
      } else {
        const result = await response.json();
        const usersData: User[] = result.data;
        setUsers(usersData);
      }
    } catch (error) {
      router.push("/not-found");
    }
  }

  return (
    <div className="flex flex-col space-y-8">
      <div className="flex md:justify-end">
        <InviteUserModal retrieveUsers={retrieveUsers} />
      </div>
      <div className="overflow-x-auto">
        <table className="grow table-auto">
          <thead className="border-b-2 border-neutral-200 font-accenture-main-semi-bold">
            <tr>
              <th className="min-w-28">Name</th>
              <th className="min-w-28">Email</th>
              <th className="min-w-32">User ID</th>
              <th className="min-w-32 px-2">User Administrator</th>
              <th className="min-w-32 px-2">Test Administrator</th>
              <th className="min-w-16"></th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr
                className="text-center text-sm odd:bg-white even:bg-neutral-50"
                key={user.sub}
              >
                <td className="py-3">
                  {user.given_name}&nbsp;{user.family_name}
                </td>
                <td className="py-3">{user.email}</td>
                <td className="py-3">{user.sub}</td>
                <td className="py-3">
                  <div className="flex justify-center">
                    {user.user_admin ? (
                      <CheckCircleIcon className="h-8 w-8 text-green-400" />
                    ) : (
                      <XCircleIcon className="h-8 w-8 text-red-400" />
                    )}
                  </div>
                </td>
                <td className="py-3">
                  <div className="flex justify-center">
                    {user.test_admin ? (
                      <CheckCircleIcon className="h-8 w-8 text-green-400" />
                    ) : (
                      <XCircleIcon className="h-8 w-8 text-red-400" />
                    )}
                  </div>
                </td>
                <td className="py-3">
                  <Link
                    href={`/admin/users/${encodeURIComponent(user.email)}`}
                    prefetch={false}
                    className="flex justify-center text-purple-700"
                  >
                    Edit
                  </Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
