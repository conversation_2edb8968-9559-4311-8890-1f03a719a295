"use client";

import { Dialog, Transition } from "@headlessui/react";
import {
  ExclamationTriangleIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/20/solid";
import { fetchAuthSession } from "aws-amplify/auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { Fragment, useEffect, useState } from "react";

export default function UserPage({ params }: { params: { user: string } }) {
  document.title = `Customer Portal`;
  const [userData, setUserData] = useState<User | null>(null);
  const [isUserAdmin, setIsUserAdmin] = useState("");
  const [isTestAdmin, setIsTestAdmin] = useState("");
  let [isOpen, setIsOpen] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const authToken = (
          await fetchAuthSession()
        ).tokens?.accessToken?.toString();
        const response = await fetch("/api/admin/users", {
          method: "POST",
          credentials: "include",
          headers: {
            Authorization: `Bearer ${authToken}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          cache: "no-store",
          body: JSON.stringify({
            username: decodeURIComponent(params.user),
          }),
        });
        if (!response.ok) {
          router.push("/not-found");
        } else {
          const content = await response.json();
          setUserData(content.data);
          setIsUserAdmin(userData?.user_admin ? "true" : "false");
          setIsTestAdmin(userData?.test_admin ? "true" : "false");
        }
      } catch (error) {
        router.push("/not-found");
      }
    };
    fetchData();
  }, [params.user, router, userData?.test_admin, userData?.user_admin]);

  // @ts-ignore
  async function setUserAdminOption(event) {
    const dropDownValue = event.target.value;
    if (dropDownValue === "false") {
      await removeUserFromUserAdmin();
      setIsUserAdmin("false");
    } else if (dropDownValue === "true") {
      await addUserToUserAdmin();
      setIsUserAdmin("true");
    } else {
      console.error("Invalid dropdown value");
    }
  }

  // @ts-ignore
  async function setTestAdminOption(event) {
    const dropDownValue = event.target.value;
    if (dropDownValue === "false") {
      await removeUserFromTestAdmin();
      setIsTestAdmin("false");
    } else if (dropDownValue === "true") {
      await addUserToTestAdmin();
      setIsTestAdmin("true");
    } else {
      console.error("Invalid dropdown value");
    }
  }
  async function addUserToTestAdmin() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    const response = await fetch(
      "/api/admin/users/groups/test_admin/add_user",
      {
        method: "POST",
        credentials: "include",
        headers: {
          Authorization: `Bearer ${authToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: decodeURIComponent(params.user),
        }),
      },
    );
  }

  async function addUserToUserAdmin() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    const response = await fetch(
      "/api/admin/users/groups/user_admin/add_user",
      {
        method: "POST",
        credentials: "include",
        headers: {
          Authorization: `Bearer ${authToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: decodeURIComponent(params.user),
        }),
      },
    );
  }

  async function removeUserFromTestAdmin() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    const response = await fetch(
      "/api/admin/users/groups/test_admin/remove_user",
      {
        method: "POST",
        credentials: "include",
        headers: {
          Authorization: `Bearer ${authToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: decodeURIComponent(params.user),
        }),
      },
    );
  }

  async function removeUserFromUserAdmin() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    const response = await fetch(
      "/api/admin/users/groups/user_admin/remove_user",
      {
        method: "POST",
        credentials: "include",
        headers: {
          Authorization: `Bearer ${authToken}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          username: decodeURIComponent(params.user),
        }),
      },
    );
  }
  async function deleteUser() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    const response = await fetch("/api/admin/users", {
      method: "DELETE",
      credentials: "include",
      headers: {
        Authorization: `Bearer ${authToken}`,
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        username: decodeURIComponent(params.user),
      }),
    });
    setIsOpen(false);
    router.push("/admin/users");
  }

  return (
    <div className="flex flex-col space-y-6 py-4 md:space-y-10">
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-10"
          onClose={() => setIsOpen(false)}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform space-y-6 overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900"
                  >
                    <div className="flex flex-row justify-between">
                      <span>
                        Delete user &quot;{decodeURIComponent(params.user)}
                        &quot;
                      </span>
                      <XMarkIcon
                        onClick={() => setIsOpen(false)}
                        className="h-6 w-6 cursor-pointer"
                      />
                    </div>
                  </Dialog.Title>
                  <div
                    className="relative flex flex-row space-x-2 rounded border border-orange-400 bg-orange-100 px-1 py-1 font-accenture-main text-orange-700"
                    role="alert"
                  >
                    <span>
                      <ExclamationTriangleIcon className="h-6 w-6 text-orange-400" />
                    </span>
                    <span className="block text-sm sm:inline md:text-base">
                      Are you sure you want to delete this account? All of their
                      data will be permanently removed. This action cannot be
                      undone.
                    </span>
                  </div>
                  <div className="flex flex-row justify-end space-x-4">
                    <button
                      className="px-8 py-1 font-accenture-main-semi-bold text-lg text-black"
                      onClick={() => setIsOpen(false)}
                    >
                      Cancel
                    </button>
                    <button
                      className="rounded-3xl bg-red-700 px-8 py-1 font-accenture-main-semi-bold text-lg text-white"
                      onClick={() => deleteUser()}
                    >
                      Delete
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      <nav className="flex" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-2 md:space-x-3">
          <li className="inline-flex items-center text-neutral-600">
            <Link href={`/admin/users`} prefetch={false}>
              User Admin
            </Link>
          </li>
          <li>&gt;</li>
          <li>
            <div className="flex items-center font-accenture-main-semi-bold text-black">
              User Details
            </div>
          </li>
        </ol>
      </nav>
      <div className="font-accenture-main-bold text-2xl md:text-3xl">
        User Details
      </div>
      <div className="grid grid-cols-1 divide-y">
        <div className="flex flex-row justify-between space-x-6 py-8">
          <div className="font-accenture-main-semi-bold">User ID</div>
          <div className="flex text-end">{userData?.sub}</div>
        </div>
        <div className="flex flex-row justify-between space-x-6 py-8">
          <div className="font-accenture-main-semi-bold">Name</div>
          <div>
            {userData?.given_name}&nbsp;{userData?.family_name}
          </div>
        </div>
        <div className="flex flex-row justify-between space-x-6 py-8">
          <div className="font-accenture-main-semi-bold">Email</div>
          <div className="flex break-all text-end">{userData?.email}</div>
        </div>
        <div className="flex flex-row justify-between space-x-6 py-8">
          <div className="font-accenture-main-semi-bold">
            User Administrator
          </div>
          <select
            className="rounded border-2 border-gray-300 py-3 pl-3 pr-28 outline-none hover:border-purple-700 focus:border-purple-700 md:pr-48"
            onChange={setUserAdminOption}
            value={isUserAdmin}
          >
            <option value="true">Yes</option>
            <option value="false">No</option>
          </select>
        </div>
        <div className="flex flex-row justify-between space-x-6 py-8">
          <div className="font-accenture-main-semi-bold">
            Test Administrator
          </div>
          <select
            className="rounded border-2 border-gray-300 py-3 pl-3 pr-28 outline-none hover:border-purple-700 focus:border-purple-700 md:pr-48"
            onChange={setTestAdminOption}
            value={isTestAdmin}
          >
            <option value="true">Yes</option>
            <option value="false">No</option>
          </select>
        </div>
        <div className="flex flex-row justify-end py-4">
          <button
            className="rounded-3xl border-2 border-purple-600 bg-white px-4 py-2 font-accenture-main-semi-bold text-purple-600"
            onClick={() => setIsOpen(true)}
          >
            <div className="flex flex-row space-x-2">
              <TrashIcon className="h-6 w-6" />
              <span>Delete user</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
}
