import TestsWithAssignedUsersTable from "@/app/admin/tests/TestsWithAssignedUsersTable";
import { runWithAmplifyServerContext } from "@/app/utils/amplifyServerUtils";
import { fetchAuthSession } from "aws-amplify/auth/server";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "Customer Portal",
};
export const dynamic = "force-dynamic";

async function getTestsWithAssignedUsers() {
  const authSession = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: (contextSpec) => fetchAuthSession(contextSpec),
  });
  const authToken = authSession.tokens?.accessToken?.toString();
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/tests`,
    {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${authToken}`,
        Accept: "application/json",
      },
    },
  );

  if (!res.ok) {
    notFound();
  }
  return res.json();
}

export default async function TestAdminPage() {
  try {
    const { data } = await getTestsWithAssignedUsers();
    const testsWithAssignedUsers: TestWithAssignedUsers[] = data;

    return (
      <div className="flex h-full flex-1 flex-col space-y-10 py-4">
        <div className="font-accenture-main-bold text-2xl md:text-3xl">
          Test Admin
        </div>
        <div className="flex flex-col overflow-x-auto">
          <TestsWithAssignedUsersTable
            testsWithAssignedUsers={testsWithAssignedUsers}
          />
        </div>
      </div>
    );
  } catch (error) {
    return <p className="flex flex-1">Something went wrong...</p>;
  }
}
