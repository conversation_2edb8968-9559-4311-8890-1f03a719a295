"use client";

import DeleteTestModal from "@/app/admin/tests/DeleteTestModal";
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PencilSquareIcon,
} from "@heroicons/react/20/solid";
import Link from "next/link";
import { useState } from "react";

type Props = {
  testsWithAssignedUsers: TestWithAssignedUsers[];
};

export default function TestsWithAssignedUsersTable({
  testsWithAssignedUsers,
}: Props) {
  const [testRows, setTestRows] = useState<TestWithAssignedUsers[]>(
    testsWithAssignedUsers,
  );

  function deleteTestRow(documentGuid: string) {
    const filteredTests = testRows.filter(
      (testRow) => testRow.document_guid !== documentGuid,
    );
    setTestRows(filteredTests);
  }

  return (
    <table className="grow table-auto">
      <thead className="border-b-2 border-neutral-200 font-accenture-main-semi-bold">
        <tr>
          <th className="min-w-28">Test ID</th>
          <th className="min-w-28">Test Name</th>
          <th className="min-w-28">Assigned Users</th>
          <th className="min-w-28">Time of Last Export</th>
          <th className="min-w-28">Export Status</th>
          <th className="min-w-16">Delete</th>
          <th className="min-w-16">Edit</th>
        </tr>
      </thead>
      <tbody>
        {testRows.map((testRow) => (
          <tr
            className="text-center text-sm odd:bg-white even:bg-neutral-50"
            key={testRow.engagement_number}
          >
            <td>{testRow.engagement_number}</td>
            <td>{testRow.name}</td>
            <td>{testRow.assigned_users.join(", ")}</td>
            <td>{testRow.time_of_last_export}</td>
            <td>
              <div className="flex justify-center">
                {testRow.export_status == "OK" ? (
                  <CheckCircleIcon className="h-8 w-8 text-green-500" />
                ) : (
                  <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
                )}
              </div>
            </td>
            <td>
              <DeleteTestModal
                deleteTestRow={deleteTestRow}
                document_guid={testRow.document_guid}
                engagement_number={testRow.engagement_number}
                name={testRow.name}
              />
            </td>
            <td className="py-3 text-purple-700">
              <div className="flex justify-center">
                <Link
                  href={`/admin/tests/${testRow.document_guid}`}
                  prefetch={false}
                >
                  <PencilSquareIcon className="h-8 w-8 cursor-pointer text-orange-500" />
                </Link>
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
}
