# Doge Enterprise Portal (DERP) API

<!-- TOC -->
* [Doge Enterprise Portal (DERP) API](#doge-enterprise-portal-derp-api)
  * [Prerequisites](#prerequisites)
    * [1. Install Python 3, uv and awscli](#1-install-python-3-uv-and-awscli)
    * [2. Create a virtual environment with all necessary dependencies](#2-create-a-virtual-environment-with-all-necessary-dependencies)
    * [3. Activate your virtual environment](#3-activate-your-virtual-environment)
    * [4. Create a `.env` file in the root of the project with the necessary variables](#4-create-a-env-file-in-the-root-of-the-project-with-the-necessary-variables)
    * [5. Run database migrations](#5-run-database-migrations)
  * [Run application](#run-application)
    * [Development mode](#development-mode)
    * [Production mode](#production-mode)
  * [Testing](#testing)
    * [With coverage](#with-coverage)
    * [With coverage and HTML output](#with-coverage-and-html-output)
  * [Linting](#linting)
  * [Formatting](#formatting)
  * [Docker](#docker)
    * [Push to AWS ECR (View push commands)](#push-to-aws-ecr-view-push-commands)
    * [Run container locally using environment variables from a `.env` file](#run-container-locally-using-environment-variables-from-a-env-file)
  * [Appendix](#appendix)
      * [Sample data](#sample-data)
    * [Connectivity to AWS deployed database through an EC2 bastion](#connectivity-to-aws-deployed-database-through-an-ec2-bastion)
    * [Create database schema from scratch with Alembic](#create-database-schema-from-scratch-with-alembic)
<!-- TOC -->

## Prerequisites

- [Python 3.13.\*](https://www.python.org/downloads/)
- [uv](https://docs.astral.sh/uv/)
- [AWS CLI with permissions to work with Cognito](https://aws.amazon.com/cli/)

### 1. Install Python 3, uv and awscli

**MacOS (using `brew`)**

```bash
brew install python@3.13 uv awscli
```

**Ubuntu/Debian/WSL**

```bash
# Add Python repository
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update

# Install Python 3.13 and pipx
sudo apt install python3.13 python3.13-venv pipx
pipx ensurepath

# Install uv
pipx install uv
```

```bash
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

Also make sure to add the necessary policies so your AWS IAM account that you are using with the AWS CLI can work with
Cognito. [An example user](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/users/details/a.panagiotopoulos.cli?section=permissions)
with
the [AmazonCognitoPowerUser](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/policies/details/arn%3Aaws%3Aiam%3A%3Aaws%3Apolicy%2FAmazonCognitoPowerUser)
policy attached.

### 2. Create a virtual environment with all necessary dependencies

From the root of the project execute:

```bash
uv sync
```

### 3. Activate your virtual environment

From the root of the project execute:

```bash
source .venv/bin/activate
```

### 4. Create a `.env` file in the root of the project with the necessary variables

```dotenv
# AWS
AWS_DEFAULT_REGION=eu-west-2

# This is the URL where your frontend runs, usually with a locally deployed Next.js app, port 3000 is used
CORS_ORIGIN_URL=http://localhost:3000

# Cognito
COGNITO_APP_CLIENT_ID=...
COGNITO_USER_POOL_ID=...

# S3
TENANT_ASSETS_BUCKET=...

# Database
DB_NAME=derp
DB_USER=derp
DB_PASSWORD=derp
DB_HOST=localhost
DB_PORT=5432
```

### 5. Run database migrations

Database migrations are performed using [Alembic](https://alembic.sqlalchemy.org/en/latest/). From the root of the
project execute:

```bash
alembic upgrade head
```

## Run application

### Development mode

```bash
fastapi dev app/main.py
```

### Production mode

```bash
fastapi run app/main.py
```

## Testing

```bash
pytest
```

### With coverage

```bash
pytest --cov=app
```

### With coverage and HTML output

```bash
pytest --cov-report html --cov=app
```

## Linting

```bash
ruff check app/* tests/*
```

## Formatting

```bash
ruff format app/* tests/*
```

## Docker

Docker images are pushed in AWS ECR in
the [derp-api repository](https://eu-west-2.console.aws.amazon.com/ecr/repositories/private/************/derp-api?region=eu-west-2).

### Push to AWS ECR ([View push commands](https://eu-west-2.console.aws.amazon.com/ecr/repositories/private/************/derp-api?region=eu-west-2))

1. Retrieve an authentication token and authenticate your Docker client to your registry. Use the AWS CLI:

    ```bash
    aws ecr get-login-password --region eu-west-2 | docker login --username AWS --password-stdin ************.dkr.ecr.eu-west-2.amazonaws.com
    ```

   Note: if you receive an error using the AWS CLI, make sure that you have the latest version of the AWS CLI and Docker
   installed.

2. Build your Docker image using the following command. For information on building a Docker file from scratch, see the
   instructions [here](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/create-container-image.html). You can
   skip this step if your image has already been built:

    ```bash
   docker build --platform=linux/amd64 -t derp-api .
   ```

3. After the build is completed, tag your image, so you can push the image to this repository.

    ```bash
    docker tag derp-api:latest ************.dkr.ecr.eu-west-2.amazonaws.com/derp-api:latest
    ```

4. Run the following command to push this image to your newly created AWS repository:

    ```bash
    docker push ************.dkr.ecr.eu-west-2.amazonaws.com/derp-api:latest
    ```

### Run container locally using environment variables from a `.env` file

```bash
docker run -d --rm -p 8000:80 --env-file ./.env --name derp-api derp-api
```

## Appendix

#### Sample data

There is some sample data
in [a SQL script
`test_data.sql` with insert statements](https://eu-west-2.console.aws.amazon.com/codesuite/codecommit/repositories/derp/browse/refs/heads/main/--/derp-api/test_data.sql?region=eu-west-2).
They create a `Context Risk Rating` system in the database, associate it with Summary and Detail attributes, and a
couple of test cases with findings.

### Connectivity to AWS deployed database through an EC2 bastion

* Add an inbound security rule to the client’s database instance to accept connections from the bastion’s security group
  on port 5432
* Get database password from AWS Secrets
* Add an inbound security rule to
  the [bastion](https://eu-west-2.console.aws.amazon.com/ec2/home?region=eu-west-2#Instances:tag:Name=derp-clienta-bastion;v=3;$case=tags:true%5C,client:false;$regex=tags:false%5C,client:false)
  to allow inbound traffic on port 22 with from your IP address
* Establish an SSH tunnel, the following command makes use of the SSH key associated with the instance, and effectively
  says: port 5431
  on your local machine will act as a tunnel to the RDS Postgres database on port 5432 through the EC2 bastion.

```bash
ssh -i "derp-clienta-bastion-key-pair.pem" -v -N -L 5431:derp-tenant-clienta-services-derpclientadbafe3df8c-hvxfciuc2bog.ceadtzohrwyh.eu-west-2.rds.amazonaws.com:5432 <EMAIL>
```

You can then add connection details pointing to your `localhost:5431`, which effectively is a tunnel to the deployed
database. [The username/password credentials currently are generated and exist in AWS Secrets Manager](https://eu-west-2.console.aws.amazon.com/secretsmanager/secret?name=derp-clienta-database-credentials-secret&region=eu-west-2)

### Create database schema from scratch with Alembic

```bash
alembic revision --autogenerate -m "initial migration"
alembic upgrade head 
```
