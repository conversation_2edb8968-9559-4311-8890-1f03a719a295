"""initial migration

Revision ID: 40e2c6ebc0c6
Revises: 
Create Date: 2024-02-26 16:49:57.710755

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '40e2c6ebc0c6'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('scoring_systems',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('id')
    )
    op.create_table('tests',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('engagement_number', sa.Integer(), nullable=False),
    sa.Column('scoring_system_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('time_of_last_export', sa.DateTime(), nullable=False),
    sa.Column('export_status', sa.Enum('OK', 'ERROR', name='exportstatus'), nullable=False),
    sa.Column('export_error', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['scoring_system_id'], ['scoring_systems.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('phases',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('guid', sa.UUID(), nullable=False),
    sa.Column('number', sa.Integer(), nullable=False),
    sa.Column('test_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['test_id'], ['tests.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('guid', 'test_id', name='uc_guid_test_id')
    )
    op.create_table('user_tests',
    sa.Column('test_id', sa.UUID(), nullable=True),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('is_test_archived', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['test_id'], ['tests.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], )
    )
    op.create_table('findings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('number', sa.Integer(), nullable=False),
    sa.Column('phase_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('base_severity', sa.String(), nullable=False),
    sa.Column('severity_additional_context', sa.String(), nullable=True),
    sa.Column('vector_string', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('OPEN', 'CLOSED', 'FALSE_POSITIVE', name='findingstatus'), server_default='OPEN', nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('recommendation', sa.Text(), nullable=True),
    sa.Column('supporting_material', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['phase_id'], ['phases.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    op.create_table('associated_hosts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('finding_id', sa.UUID(), nullable=False),
    sa.Column('ip_address', postgresql.INET(), nullable=False),
    sa.Column('network', sa.String(), nullable=False),
    sa.Column('port', sa.Integer(), nullable=False),
    sa.Column('protocol', sa.String(), nullable=False),
    sa.ForeignKeyConstraint(['finding_id'], ['findings.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('associated_hosts')
    op.drop_table('findings')
    op.drop_table('user_tests')
    op.drop_table('phases')
    op.drop_table('tests')
    op.drop_table('users')
    op.drop_table('scoring_systems')
    # ### end Alembic commands ###
