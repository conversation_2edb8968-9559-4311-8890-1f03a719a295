import boto3
import structlog
from botocore.exceptions import ClientError
from pydantic import EmailStr

from app.config import get_settings

logger = structlog.get_logger()

client = boto3.client("cognito-idp")
GROUP_TEST_ADMINS = "test_admins"
GROUP_USER_ADMINS = "user_admins"


class CognitoError(Exception):
    pass


class UsernameExistsError(Exception):
    pass


async def get_users():
    """Get all users in the User Pool and the groups they belong to, if they are a User Admin and/or a Test Admin."""
    try:
        users_response = client.list_users(
            UserPoolId=get_settings().cognito_user_pool_id,
        )
        users = []
        for user_response in users_response["Users"]:
            user = {}
            for user_response_attribute in user_response["Attributes"]:
                user[user_response_attribute["Name"]] = user_response_attribute["Value"]

            user_groups = client.admin_list_groups_for_user(
                Username=user["email"],
                UserPoolId=get_settings().cognito_user_pool_id,
            )
            user["test_admin"] = False
            user["user_admin"] = False
            for group in user_groups["Groups"]:
                if group["GroupName"] == GROUP_USER_ADMINS:
                    user["user_admin"] = True
                if group["GroupName"] == GROUP_TEST_ADMINS:
                    user["test_admin"] = True
            users.append(user)
        return users
    except Exception as e:
        logger.error(
            "Could not retrieve users from Cognito User Pool",
            cognito_user_pool_id=get_settings().cognito_user_pool_id,
            error=str(e),
        )
        raise CognitoError("Failed to retrieve users")


async def delete_user(username: EmailStr):
    """Delete user from User Pool.

    Args:
        username (EmailStr): User email
    """
    try:
        response = client.admin_delete_user(
            UserPoolId=get_settings().cognito_user_pool_id, Username=username
        )
        logger.info(
            "Deleted user",
            user=username,
        )
        return response
    except Exception as e:
        logger.error(
            "Failed to delete user",
            user=username,
            error=str(e),
        )
        raise CognitoError(f"Failed to delete user {username}")


async def add_user_to_user_admins(username: EmailStr):
    """Add user to Test Admins group.

    Args:
        username: User email
    """
    try:
        response = client.admin_add_user_to_group(
            UserPoolId=get_settings().cognito_user_pool_id,
            Username=username,
            GroupName=GROUP_USER_ADMINS,
        )
        logger.info(
            "Added user to group",
            user=username,
            group=GROUP_USER_ADMINS,
        )
        return response
    except Exception as e:
        logger.error(
            "Failed to add user to group",
            user=username,
            group=GROUP_USER_ADMINS,
            error=str(e),
        )
        raise CognitoError(
            f"Failed to add user {username} to group {GROUP_USER_ADMINS}"
        )


async def remove_user_from_user_admins(username: EmailStr):
    """Remove user from User Admins group.

    Args:
        username: User email

    """
    try:
        response = client.admin_remove_user_from_group(
            UserPoolId=get_settings().cognito_user_pool_id,
            Username=username,
            GroupName=GROUP_USER_ADMINS,
        )
        logger.info(
            "Removed user from group",
            user=username,
            group=GROUP_USER_ADMINS,
        )
        return response
    except Exception as e:
        logger.error(
            "Failed to remove user from group",
            user=username,
            group=GROUP_USER_ADMINS,
            error=str(e),
        )
        raise CognitoError(
            f"Failed to remove user {username} from group {GROUP_USER_ADMINS}"
        )


async def add_user_to_test_admins(username: EmailStr):
    """Add user to Test Admins group.

    Args:
        username (EmailStr): User email
    """
    try:
        response = client.admin_add_user_to_group(
            UserPoolId=get_settings().cognito_user_pool_id,
            Username=username,
            GroupName=GROUP_TEST_ADMINS,
        )
        logger.info(
            "Added user to group",
            user=username,
            group=GROUP_TEST_ADMINS,
        )
        return response
    except Exception as e:
        logger.error(
            "Failed to add user to group",
            user=username,
            group=GROUP_TEST_ADMINS,
            error=str(e),
        )
        raise CognitoError(
            f"Failed to add user {username} to group {GROUP_TEST_ADMINS}"
        )


async def remove_user_from_test_admins(username: EmailStr):
    """Remove user from Test Admins group.

    Args:
        username (EmailStr): User email
    """
    try:
        response = client.admin_remove_user_from_group(
            UserPoolId=get_settings().cognito_user_pool_id,
            Username=username,
            GroupName=GROUP_TEST_ADMINS,
        )
        logger.info(
            "Removed user from group",
            user=username,
            group=GROUP_TEST_ADMINS,
        )
        return response
    except Exception as e:
        logger.error(
            "Failed to remove user from group",
            user=username,
            group=GROUP_TEST_ADMINS,
            error=str(e),
        )
        raise CognitoError(
            f"Failed to remove user {username} from group {GROUP_TEST_ADMINS}"
        )


async def get_user(username: EmailStr):
    """Get user details from Cognito.

    Args:
        username (EmailStr): User Email
    """
    try:
        user_response = client.admin_get_user(
            UserPoolId=get_settings().cognito_user_pool_id, Username=username
        )
    except Exception as e:
        logger.error(
            "Could not retrieve user details from Cognito User Pool",
            cognito_user_pool_id=get_settings().cognito_user_pool_id,
            username=username,
            error=str(e),
        )
        raise CognitoError(f"Failed to retrieve user {username}")

    try:
        user = dict()
        for user_response_attribute in user_response["UserAttributes"]:
            user[user_response_attribute["Name"]] = user_response_attribute["Value"]

        user_groups = client.admin_list_groups_for_user(
            Username=user["email"],
            UserPoolId=get_settings().cognito_user_pool_id,
        )
        user["test_admin"] = False
        user["user_admin"] = False
        for group in user_groups["Groups"]:
            if group["GroupName"] == GROUP_USER_ADMINS:
                user["user_admin"] = True
            if group["GroupName"] == GROUP_TEST_ADMINS:
                user["test_admin"] = True
        return user
    except Exception as e:
        logger.error(
            "Could not list user groups for user in Cognito User Pool",
            cognito_user_pool_id=get_settings().cognito_user_pool_id,
            username=username,
            error=str(e),
        )
        raise CognitoError(f"Failed to retrieve user {username}")


async def invite_user(email: str) -> None:
    """Invite a new user to the Portal.

    Args:
        email (str): The email address of the newly invited user

    Raises:
        UsernameExistsError: If the user email already exists
        CognitoError: If the user could not be invited

    Returns:
        None

    """
    try:
        client.admin_create_user(
            UserPoolId=get_settings().cognito_user_pool_id,
            Username=email,
            DesiredDeliveryMediums=["EMAIL"],
        )
    except ClientError as e:
        if e.response["Error"]["Code"] == "UsernameExistsException":
            logger.error(
                "An account with the given email already exists",
                cognito_user_pool_id=get_settings().cognito_user_pool_id,
                email=email,
                error=str(e),
            )
            raise UsernameExistsError("An account with the given email already exists")
    except Exception as e:
        logger.error(
            "Failed to invite user",
            cognito_user_pool_id=get_settings().cognito_user_pool_id,
            email=email,
            error=str(e),
        )
        raise CognitoError(f"Failed to invite user {email}")


async def search_users(search_string: str) -> list[str]:
    """Search and retrieve users based on input string.

    Args:
        search_string (str): The search string

    Raises:
        CognitoError: Raised when searching a user in a Cognito User Pool fails

    Returns:
        list[str]: List of users' emails
    """
    users: list[str] = []

    try:
        response = client.list_users(
            UserPoolId=get_settings().cognito_user_pool_id,
            AttributesToGet=["email"],
            Filter=f"email ^= '{search_string}'",
        )

        for user in response["Users"]:
            for user_attribute in user["Attributes"]:
                if user_attribute["Name"] == "email":
                    users.append(user_attribute["Value"])

        return users
    except KeyError as e:
        logger.error(
            "Invalid user attribute in user search",
            cognito_user_pool_id=get_settings().cognito_user_pool_id,
            search_string=search_string,
            error=str(e),
        )
        raise CognitoError("Invalid user attribute")
    except ClientError as e:
        logger.error(
            "User search error",
            cognito_user_pool_id=get_settings().cognito_user_pool_id,
            search_string=search_string,
            error=str(e),
        )
        raise CognitoError("Search error")
