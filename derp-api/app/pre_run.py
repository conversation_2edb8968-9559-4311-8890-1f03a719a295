import structlog
from sqlalchemy.dialects.postgresql import insert

from app.database import engine
from app.models import ScoringSystem

logger = structlog.get_logger()


def prepopulate_db() -> None:
    """Prepopulate the database with scoring systems if they don't exist.

    Returns: None
    """
    with engine.connect() as conn:
        stmt = (
            insert(ScoringSystem)
            .values(
                name="ContextRiskRating",
            )
            .on_conflict_do_nothing(index_elements=["name"])
        )
        conn.execute(stmt)

        stmt = (
            insert(ScoringSystem)
            .values(
                name="CVSS3Rating",
            )
            .on_conflict_do_nothing(index_elements=["name"])
        )
        conn.execute(stmt)

        stmt = (
            insert(ScoringSystem)
            .values(
                name="CVSS31Rating",
            )
            .on_conflict_do_nothing(index_elements=["name"])
        )
        conn.execute(stmt)
        conn.commit()
        logger.info(
            "Prepopulated database with scoring systems",
            scoring_systems=["ContextRiskRating", "CVSS3Rating", "CVSS31Rating"],
        )
