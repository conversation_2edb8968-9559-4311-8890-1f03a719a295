import uuid

import fastapi
import structlog
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware

from app.config import get_settings
from app.database import create_database_tables
from app.pre_run import prepopulate_db
from app.routers import admin_tests, admin_users, tests, users

logger = structlog.get_logger()

"""
Create database tables.

Normally this process should be taken care of by alembic, which already exists in the project.
Leaving it here as an alternative for development purposes.

Comment out if using alembic.
"""
create_database_tables()
prepopulate_db()

app = FastAPI()

origins = [get_settings().cors_origin_url]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def logger_middleware(request: Request, call_next) -> fastapi.Response:
    """Logger middleware.

    Args:
        request (Request): FastAPI request object
        call_next (f): Function to call after the middleware

    Returns:
        fastapi.Response
    """
    structlog.contextvars.clear_contextvars()
    structlog.contextvars.bind_contextvars(
        path=request.url.path,
        method=request.method,
        client_host=request.client.host,
        request_id=str(uuid.uuid4()),
    )
    response: fastapi.Response = await call_next(request)

    structlog.contextvars.bind_contextvars(
        status_code=response.status_code,
    )

    # Exclude /healthcheck endpoint from producing logs
    if request.url.path != "/healthcheck":
        if 400 <= response.status_code < 500:
            logger.warn("Client error")
        elif response.status_code >= 500:
            logger.error("Server error")
        else:
            logger.info("OK")

    return response


@app.get("/healthcheck")
async def healthcheck():
    """Healthcheck.

    Returns:
        fastapi.Response
    """
    return Response()


app.include_router(tests.router, prefix="/tests", tags=["tests"])

app.include_router(users.router, prefix="/users", tags=["users"])

app.include_router(
    admin_users.router,
    prefix="/admin/users",
    tags=["admin, users"],
)

app.include_router(
    admin_tests.router,
    prefix="/admin/tests",
    tags=["admin, tests"],
)
