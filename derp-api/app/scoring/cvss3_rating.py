from cvss import CVSS3, constants3
from pydantic import ValidationError

from app.scoring.scoring_system import BaseScoringSystem, MetricGroups


class CVSS3Rating(BaseScoringSystem):
    def __init__(self, vector_string: str):
        """Parse and validate a CVSSv3 vector string.

        Args:
            vector_string: The CVSSv3 vector string
        Raises:
            CVSS3MalformedError: Exception for malformed input CVSS3 vectors.
        """
        self.vector_string = vector_string

        self.cvssv3_mandatory_metrics: list[dict[str, str]] = []
        self.cvssv3_temporal_metrics: list[dict[str, str]] = []
        self.cvssv3_environmental_metrics: list[dict[str, str]] = []
        self._parse_vector()

    def _parse_vector(self) -> None:
        c = CVSS3(self.vector_string)
        for metric_attribute, metric_value in c.metrics.items():
            if metric_attribute in constants3.METRICS_MANDATORY:
                self.cvssv3_mandatory_metrics.append(
                    {
                        "attribute": constants3.METRICS_ABBREVIATIONS[metric_attribute],
                        "value": c.get_value_description(metric_attribute).lower(),
                    }
                )

            if metric_attribute in constants3.TEMPORAL_METRICS:
                self.cvssv3_temporal_metrics.append(
                    {
                        "attribute": constants3.METRICS_ABBREVIATIONS[metric_attribute],
                        "value": c.get_value_description(metric_attribute).lower(),
                    }
                )

            if metric_attribute in constants3.ENVIRONMENTAL_METRICS:
                self.cvssv3_environmental_metrics.append(
                    {
                        "attribute": constants3.METRICS_ABBREVIATIONS[metric_attribute],
                        "value": c.get_value_description(metric_attribute).lower(),
                    }
                )

    def export(self) -> MetricGroups:
        """Export all the metrics using CVSSv3 metric groups.

        Returns:
            dict: A dictionary of CVSSv3 metrics in groups

        """
        try:
            metric_groups = MetricGroups(
                **{
                    "metric_groups": [
                        {
                            "title": "mandatory",
                            "metrics": self.cvssv3_mandatory_metrics,
                        },
                        {"title": "temporal", "metrics": self.cvssv3_temporal_metrics},
                        {
                            "title": "environmental",
                            "metrics": self.cvssv3_environmental_metrics,
                        },
                    ]
                }
            )
            return metric_groups
        except ValidationError as e:
            print(str(e))
