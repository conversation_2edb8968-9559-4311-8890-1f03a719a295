import abc
from abc import ABC

from pydantic import BaseModel


class Metric(BaseModel):
    attribute: str
    value: str


class MetricGroup(BaseModel):
    title: str
    metrics: list[Metric]


class MetricGroups(BaseModel):
    metric_groups: list[MetricGroup]


class BaseScoringSystem(ABC):
    @abc.abstractmethod
    def __init__(self, vector_string: str):
        """Base class for scoring systems.

        Args:
            vector_string (str): The vector string describing scoring attributes
        """
        self.vector_string = vector_string

    def _parse_vector(self) -> None:
        """Parses metrics from the scoring vector."""
        pass

    @abc.abstractmethod
    def export(self) -> MetricGroups:
        """Export all metrics using groups.

        Returns:
            MetricGroups: All metrics aggregated in groups

        """
        pass
