{"availability-zones:account=************:region=eu-west-2": ["eu-west-2a", "eu-west-2b", "eu-west-2c"], "vpc-provider:account=************:filter.vpc-id=vpc-00919ca5e4a33129e:region=eu-west-2:returnAsymmetricSubnets=true": {"vpcId": "vpc-00919ca5e4a33129e", "vpcCidrBlock": "10.0.0.0/16", "ownerAccountId": "************", "availabilityZones": [], "subnetGroups": [{"name": "Isolated", "type": "Isolated", "subnets": [{"subnetId": "subnet-0bdef3b59bed65f99", "cidr": "10.0.128.0/20", "availabilityZone": "eu-west-2a", "routeTableId": "rtb-03d39bf00cd11200a"}, {"subnetId": "subnet-0e13efef53f96e68d", "cidr": "10.0.160.0/20", "availabilityZone": "eu-west-2a", "routeTableId": "rtb-04e2fdea90822b090"}, {"subnetId": "subnet-056f59c4b61ba8a31", "cidr": "10.0.176.0/20", "availabilityZone": "eu-west-2b", "routeTableId": "rtb-086c5243ce5e715fc"}, {"subnetId": "subnet-083a277a278e00cc0", "cidr": "10.0.144.0/20", "availabilityZone": "eu-west-2b", "routeTableId": "rtb-06911a586f960a9d2"}]}, {"name": "Public", "type": "Public", "subnets": [{"subnetId": "subnet-05b570d4820c99c0e", "cidr": "10.0.0.0/20", "availabilityZone": "eu-west-2a", "routeTableId": "rtb-0346599c065d67ebf"}, {"subnetId": "subnet-0a604d938503d5fbc", "cidr": "10.0.16.0/20", "availabilityZone": "eu-west-2b", "routeTableId": "rtb-0346599c065d67ebf"}]}]}}