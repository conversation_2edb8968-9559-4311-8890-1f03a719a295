import * as cdk from "aws-cdk-lib";
import { RemovalPolicy } from "aws-cdk-lib";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import * as cognito from "aws-cdk-lib/aws-cognito";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import { Repository } from "aws-cdk-lib/aws-ecr";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as ecs_patterns from "aws-cdk-lib/aws-ecs-patterns";
import * as elbv2 from "aws-cdk-lib/aws-elasticloadbalancingv2";
import {
  ApplicationTargetGroup,
  CfnLoadBalancer,
  SslPolicy,
} from "aws-cdk-lib/aws-elasticloadbalancingv2";
import * as iam from "aws-cdk-lib/aws-iam";
import * as kms from "aws-cdk-lib/aws-kms";
import * as lambda from "aws-cdk-lib/aws-lambda";
import * as eventsources from "aws-cdk-lib/aws-lambda-event-sources";
import * as rds from "aws-cdk-lib/aws-rds";
import { Credentials } from "aws-cdk-lib/aws-rds";
import * as aws_route53 from "aws-cdk-lib/aws-route53";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as sns from "aws-cdk-lib/aws-sns";
import * as subscriptions from "aws-cdk-lib/aws-sns-subscriptions";
import * as sqs from "aws-cdk-lib/aws-sqs";
import { Construct } from "constructs";
import * as path from "path";

export class DerpTenantFidelityServicesStack extends cdk.Stack {
  constructor(
    scope: Construct,
    id: string,
    tenant: string,
    baseDomain: string,
    awsRegion: string,
    vpc: ec2.Vpc,
    centralListener: elbv2.ApplicationListener,
    listenerRulePriority: number,
    cognitoUserPoolId: string,
    cognitoAppClientId: string,
    dogeToDerpSnsTopic: sns.Topic,
    centralAssetsBucket: s3.Bucket,
    props?: cdk.StackProps,
  ) {
    super(scope, id, props);

    // Hosted zones
    // https://us-east-1.console.aws.amazon.com/route53/v2/hostedzones#ListRecordSets/Z081838920NNMZMGSTE6D
    const privateHostedZoneId = "Z081838920NNMZMGSTE6D";

    // Database
    const databaseName = "derp";
    const databaseCredentialsSecret = new secretsmanager.Secret(
      this,
      `derp-${tenant}-database-credentials-secret`,
      {
        secretName: `derp-${tenant}-database-credentials-secret`,
        generateSecretString: {
          secretStringTemplate: JSON.stringify({ username: "postgres" }),
          generateStringKey: "password",
          excludeCharacters: '/@"',
        },
      },
    );

    // Database
    const databaseInstance = new rds.DatabaseInstance(
      this,
      `derp-${tenant}-db`,
      {
        caCertificate: rds.CaCertificate.RDS_CA_RDS2048_G1,
        engine: rds.DatabaseInstanceEngine.postgres({
          version: rds.PostgresEngineVersion.VER_16_2,
        }),
        instanceType: ec2.InstanceType.of(
          ec2.InstanceClass.BURSTABLE3,
          ec2.InstanceSize.MICRO,
        ),
        // Important!!! Without encrypted storage, RDS instance deployments in the Accenture AWS environment fail silently
        storageEncrypted: true,
        databaseName: databaseName,
        credentials: Credentials.fromSecret(databaseCredentialsSecret),
        vpc: vpc,
        vpcSubnets: { subnetGroupName: `derp-${tenant}-private-isolated` },
      },
    );

    // Fargate cluster
    const cluster = new ecs.Cluster(this, `derp-${tenant}-cluster`, {
      clusterName: `derp-${tenant}-cluster`,
      containerInsights: true,
      vpc: vpc,
    });

    // ECR repositories
    // https://eu-west-2.console.aws.amazon.com/ecr/repositories/private/255229891353/derp-api?region=eu-west-2
    const derpApiImageRepository = Repository.fromRepositoryArn(
      this,
      `derp-${tenant}-api-docker-image-repository`,
      "arn:aws:ecr:eu-west-2:255229891353:repository/derp-api",
    );

    // https://eu-west-2.console.aws.amazon.com/ecr/repositories/private/255229891353/derp-ui?region=eu-west-2
    const derpUiImageRepository = Repository.fromRepositoryArn(
      this,
      `derp-${tenant}-ui-docker-image-repository`,
      "arn:aws:ecr:eu-west-2:255229891353:repository/derp-ui",
    );

    // ACM certificate - already existing, importing it using its arn
    // https://eu-west-2.console.aws.amazon.com/acm/home?region=eu-west-2#/certificates/c1072314-e82a-46bf-adfd-27171fe77896
    const arnCertificate =
      "arn:aws:acm:eu-west-2:255229891353:certificate/c1072314-e82a-46bf-adfd-27171fe77896";
    const certificate = acm.Certificate.fromCertificateArn(
      this,
      `derp-${tenant}-certificate`,
      arnCertificate,
    );

    // S3 bucket containing assets for a tenant
    const tenantAssetsBucket = new s3.Bucket(
      this,
      `derp-${tenant}-assets-bucket`,
      {
        bucketName: `derp-${tenant}-assets-bucket`,
        blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
        encryption: s3.BucketEncryption.S3_MANAGED,
        enforceSSL: true,
        versioned: true,
        removalPolicy: RemovalPolicy.RETAIN,
      },
    );

    // TODO Add replication rule from central bucket based on Tags  with format of
    // key - value
    // const cfnBucket = centralAssetsBucket.node.defaultChild as s3.CfnBucket;
    // cfnBucket.replicationConfiguration = {
    //   role: "myrole",
    //   rules: [
    //     {
    //       destination: {
    //         bucket: tenantAssetsBucket.bucketName,
    //       },
    //       status: "Enabled",
    //       filter: {
    //         tagFilter: { key: "tenant", value: tenant },
    //       },
    //     },
    //   ],
    // };

    // Load-balanced Fargate service Derp API
    const derpApiServiceSecurityGroup = new ec2.SecurityGroup(
      this,
      `derp-${tenant}-api-service-security-group`,
      {
        vpc: vpc,
        allowAllOutbound: false,
        allowAllIpv6Outbound: false,
      },
    );
    derpApiServiceSecurityGroup.addEgressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(443),
      "Allow outbound traffic to the Derp API",
    );
    const loadBalancedFargateServiceDerpApi =
      new ecs_patterns.ApplicationLoadBalancedFargateService(
        this,
        `derp-${tenant}-api-service`,
        {
          serviceName: `derp-${tenant}-api-service`,
          securityGroups: [derpApiServiceSecurityGroup],
          protocol: elbv2.ApplicationProtocol.HTTPS,
          sslPolicy: SslPolicy.RECOMMENDED_TLS,
          certificate: certificate,
          domainName: `${tenant}.api.private.${baseDomain}`,
          domainZone: aws_route53.HostedZone.fromHostedZoneAttributes(
            this,
            `derp-${tenant}-api-hosted-zone`,
            {
              zoneName: baseDomain,
              hostedZoneId: privateHostedZoneId,
            },
          ),
          redirectHTTP: true,
          cluster: cluster,
          cpu: 256,
          desiredCount: 1,
          taskSubnets: {
            subnetGroupName: `derp-${tenant}-private-with-egress`,
            onePerAz: true,
          },
          loadBalancerName: `derp-${tenant}-api-alb`,
          taskImageOptions: {
            image: ecs.ContainerImage.fromEcrRepository(
              derpApiImageRepository,
              "latest",
            ),
            enableLogging: true,
            containerPort: 80,
            logDriver: ecs.LogDrivers.awsLogs({
              streamPrefix: `derp-${tenant}-api-logs`,
            }),
            secrets: {
              DB_USER: ecs.Secret.fromSecretsManager(
                databaseCredentialsSecret,
                "username",
              ),
              DB_PASSWORD: ecs.Secret.fromSecretsManager(
                databaseCredentialsSecret,
                "password",
              ),
            },
            environment: {
              DB_NAME: databaseName,
              DB_HOST: databaseInstance.dbInstanceEndpointAddress,
              DB_PORT: databaseInstance.dbInstanceEndpointPort,
              AWS_DEFAULT_REGION: awsRegion,
              COGNITO_USER_POOL_ID: cognitoUserPoolId,
              COGNITO_APP_CLIENT_ID: cognitoAppClientId,
              TENANT_ASSETS_BUCKET: tenantAssetsBucket.bucketName,
              CORS_ORIGIN_URL: `https://${tenant}.ui.private.${baseDomain}`,
            },
          },
          memoryLimitMiB: 512,
          assignPublicIp: false,
          publicLoadBalancer: false,
        },
      );

    // Grant read access to the assets bucket from the API
    tenantAssetsBucket.grantRead(
      loadBalancedFargateServiceDerpApi.taskDefinition.taskRole,
    );

    const private_subnets_selection = vpc.selectSubnets({
      subnetGroupName: `derp-${tenant}-private-with-egress`,
    });
    const cfnLoadBalancerApi = loadBalancedFargateServiceDerpApi.loadBalancer
      .node.defaultChild as CfnLoadBalancer;
    cfnLoadBalancerApi.subnets = private_subnets_selection.subnets.map(
      (subnet) => subnet.subnetId,
    );
    loadBalancedFargateServiceDerpApi.targetGroup.configureHealthCheck({
      path: "/healthcheck",
    });

    databaseCredentialsSecret.grantRead(
      loadBalancedFargateServiceDerpApi.taskDefinition.taskRole,
    );
    const cognitoUserPool = cognito.UserPool.fromUserPoolId(
      this,
      `derp-${tenant}-user-pool-imported`,
      cognitoUserPoolId,
    );
    cognitoUserPool.grant(
      loadBalancedFargateServiceDerpApi.taskDefinition.taskRole,
      "cognito-idp:*",
    );
    loadBalancedFargateServiceDerpApi.service.connections.allowToDefaultPort(
      databaseInstance,
    );

    // UI
    const derpUiTaskDefinition = new ecs.FargateTaskDefinition(
      this,
      `derp-${tenant}-task-definition`,
      {
        memoryLimitMiB: 512,
        cpu: 256,
      },
    );

    // Add a container to your task
    derpUiTaskDefinition.addContainer(`derp-${tenant}-ui-container`, {
      image: ecs.ContainerImage.fromEcrRepository(
        derpUiImageRepository,
        tenant,
      ),
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: `derp-${tenant}-ui-logs`,
      }),
      portMappings: [
        {
          containerPort: 3000,
        },
      ],
    });

    // Load-balanced Fargate service Derp UI
    const derpUiServiceSecurityGroup = new ec2.SecurityGroup(
      this,
      `derp-${tenant}-ui-service-security-group`,
      {
        vpc: vpc,
        allowAllOutbound: false,
        allowAllIpv6Outbound: false,
      },
    );
    derpUiServiceSecurityGroup.addEgressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(443),
      "Allow outbound traffic to the Derp UI",
    );

    const targetGroup = new ApplicationTargetGroup(
      this,
      `derp-${tenant}-ui-target-group`,
      {
        vpc: vpc,
        targetGroupName: `derp-${tenant}-ui-target-group`,
        healthCheck: { path: "/" },
        protocol: elbv2.ApplicationProtocol.HTTP,
        port: 3000,
        protocolVersion: elbv2.ApplicationProtocolVersion.HTTP1,
      },
    );

    const derpUiFargateService = new ecs.FargateService(
      this,
      `derp-${tenant}-ui-service`,
      {
        cluster,
        serviceName: `derp-${tenant}-ui-service`,
        assignPublicIp: false,
        taskDefinition: derpUiTaskDefinition,
        securityGroups: [derpUiServiceSecurityGroup],
        desiredCount: 1,
        vpcSubnets: { subnetGroupName: `derp-${tenant}-private-with-egress` },
      },
    );
    targetGroup.addTarget(derpUiFargateService);

    // Production IP addresses rule
    const applicationListenerRule = new elbv2.ApplicationListenerRule(
      this,
      `derp-${tenant}-application-listener-rule`,
      {
        listener: centralListener,
        priority: listenerRulePriority,
        conditions: [
          elbv2.ListenerCondition.hostHeaders([`${tenant}.${baseDomain}`]),
          // elbv2.ListenerCondition.sourceIps([
          //   "**************/32",
          //   "**********/32",
          //   "************/32",
          //   "************/32",
          // ]),
        ],
        action: elbv2.ListenerAction.forward([targetGroup]),
      },
    );

    // Disaster Recovery IP addresses rule
    // const applicationListenerRuleDisasterRecovery =
    //   new elbv2.ApplicationListenerRule(
    //     this,
    //     `derp-${tenant}-disaster-recovery-application-listener-rule`,
    //     {
    //       listener: centralListener,
    //       priority: listenerRulePriority + 1,
    //       conditions: [
    //         elbv2.ListenerCondition.hostHeaders([`${tenant}.${baseDomain}`]),
    //         elbv2.ListenerCondition.sourceIps([
    //           "**************/32",
    //           "***********/32",
    //           "************/32",
    //           "***********/32",
    //         ]),
    //       ],
    //       action: elbv2.ListenerAction.forward([targetGroup]),
    //     },
    //   );
    /*
    Bastion key pair
    To download the private key of the key pair follow these instructions:
    https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/create-key-pairs.html#create-key-pair-cloudformation
      * Use the describe-key-pairs command as follows to get the ID of the key pair, where "new-key-pair" is defined as "keyName" in the CDK struct below.
        aws ec2 describe-key-pairs --filters Name=key-name,Values=new-key-pair --query KeyPairs[*].KeyPairId --output text
        The following is example output: key-05abb699beEXAMPLE
      * Use the get-parameter command as follows to get the parameter for your key and save the key material in a .pem file.
        aws ssm get-parameter --name /ec2/keypair/key-05abb699beEXAMPLE --with-decryption --query Parameter.Value --output text > new-key-pair.pem
     */
    const bastionKeyPair = new ec2.CfnKeyPair(
      this,
      `derp-${tenant}-bastion-key-pair`,
      {
        keyName: `derp-${tenant}-bastion-key-pair`,
        keyFormat: "pem",
        keyType: "ed25519",
        tags: [
          {
            key: "project",
            value: "derp",
          },
          {
            key: "tenant",
            value: tenant,
          },
        ],
      },
    );

    // Tenant bastion EC2 instance
    // const bastionInstance = new ec2.Instance(this, `derp-${tenant}-bastion`, {
    //   vpc: vpc,
    //   vpcSubnets: { subnetGroupName: `derp-${tenant}-public` },
    //   keyName: bastionKeyPair.keyName,
    //   instanceType: ec2.InstanceType.of(
    //     ec2.InstanceClass.BURSTABLE2,
    //     ec2.InstanceSize.MICRO,
    //   ),
    //   machineImage: new ec2.AmazonLinuxImage({
    //     generation: ec2.AmazonLinuxGeneration.AMAZON_LINUX_2,
    //   }),
    // });
    //
    // databaseInstance.grantConnect(bastionInstance);

    // const cfnLoadBalancerUi = loadBalancedFargateServiceDerpUi.loadBalancer.node
    //   .defaultChild as CfnLoadBalancer;
    // cfnLoadBalancerUi.subnets = [
    //   privateSubnet1.subnetId,
    //   privateSubnet2.subnetId,
    // ];

    // KMS encryption key that encrypts data in transit for the SQS of the tenant
    const sqsEncryptionKey = new kms.Key(
      this,
      `derp-${tenant}-sqs-encryption-key`,
      {
        enableKeyRotation: true,
      },
    );
    sqsEncryptionKey.addAlias(`derp-${tenant}-sqs-encryption-key`);

    sqsEncryptionKey.addToResourcePolicy(
      new iam.PolicyStatement({
        sid: "Allow the usage of the key by the SNS between Doge and Derp",
        effect: iam.Effect.ALLOW,
        resources: [dogeToDerpSnsTopic.topicArn],
        principals: [new iam.ServicePrincipal("sns.amazonaws.com")],
        actions: ["kms:Decrypt", "kms:GenerateDataKey*"],
      }),
    );

    const queue = new sqs.Queue(this, `derp-${tenant}-queue.fifo`, {
      enforceSSL: true,
      encryption: sqs.QueueEncryption.KMS,
      encryptionMasterKey: sqsEncryptionKey,
      fifo: true,
    });

    // Subscribe SQS to SNS and filter for messages only related to the tenant
    dogeToDerpSnsTopic.addSubscription(
      new subscriptions.SqsSubscription(queue, {
        filterPolicy: {
          tenant: sns.SubscriptionFilter.stringFilter({
            allowlist: [`${tenant}`],
          }),
        },
      }),
    );

    // Lambda function that adds events coming from a client SQS to the portal database
    // The actual code exists in the monorepo on its dedicated folder
    // Official AWS CDK example: https://github.com/aws-samples/cdk-lambda-bundling/blob/main/lib/cdk-bundling-lambda-stack.ts
    const lambdaSecurityGroup = new ec2.SecurityGroup(
      this,
      `derp-${tenant}-lambda-security-group`,
      {
        vpc,
        allowAllOutbound: false,
        allowAllIpv6Outbound: false,
      },
    );
    const lambdaAddDataToPortal = new lambda.DockerImageFunction(
      this,
      `derp-${tenant}-add-data-to-portal`,
      {
        securityGroups: [lambdaSecurityGroup],
        description: `Lambda function that writes messages from the SQS of Derp tenant ${tenant} to its dedicated database`,
        vpc: vpc,
        functionName: `derp-${tenant}-add-data-to-portal`,
        code: lambda.DockerImageCode.fromImageAsset(
          path.join(__dirname, "../../lambda-add-client-case-to-portal"),
        ),
        memorySize: 512,
        timeout: cdk.Duration.seconds(10),
        environment: {
          DB_USER: databaseCredentialsSecret
            .secretValueFromJson("username")
            .unsafeUnwrap(),
          DB_PASSWORD: databaseCredentialsSecret
            .secretValueFromJson("password")
            .unsafeUnwrap(),
          DB_NAME: databaseName,
          DB_HOST: databaseInstance.dbInstanceEndpointAddress,
          DB_PORT: databaseInstance.dbInstanceEndpointPort,
        },
      },
    );
    databaseInstance.grantConnect(lambdaAddDataToPortal);
    databaseInstance.connections.allowFrom(
      lambdaAddDataToPortal,
      ec2.Port.tcp(databaseInstance.instanceEndpoint.port),
    );
    queue.grantConsumeMessages(lambdaAddDataToPortal);

    // Add the SQS as an event source to the lambda function
    lambdaAddDataToPortal.addEventSource(
      new eventsources.SqsEventSource(queue),
    );
  }
}
