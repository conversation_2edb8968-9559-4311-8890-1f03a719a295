import * as cdk from "aws-cdk-lib";
import * as cognito from "aws-cdk-lib/aws-cognito";
import { Construct } from "constructs";

export class DerpDefaultCognitoStack extends cdk.Stack {
  public readonly cognitoUserPoolId: string;
  public readonly cognitoAppClientId: string;

  constructor(
    scope: Construct,
    id: string,
    tenant: string,
    props?: cdk.StackProps,
  ) {
    super(scope, id, props);

    // Cognito
    const cognitoUserPool = new cognito.UserPool(
      this,
      `derp-${tenant}-cognito-user-pool`,
      {
        userPoolName: `derp-${tenant}-cognito-user-pool`,
        signInAliases: {
          email: true,
        },
        mfa: cognito.Mfa.REQUIRED,
        mfaSecondFactor: {
          sms: false,
          otp: true,
        },
        standardAttributes: {
          givenName: {
            required: true,
            mutable: true,
          },
          familyName: {
            required: true,
            mutable: true,
          },
          email: {
            required: true,
            mutable: true,
          },
        },
        selfSignUpEnabled: false,
        keepOriginal: {
          email: true,
        },
      },
    );

    new cognito.CfnUserPoolGroup(this, `derp-${tenant}-test-admins-group`, {
      userPoolId: cognitoUserPool.userPoolId,
      description: "Group of Test Admins for tenant default",
      groupName: "test_admins",
    });

    new cognito.CfnUserPoolGroup(this, `derp-${tenant}-user-admins-group`, {
      userPoolId: cognitoUserPool.userPoolId,
      description: "Group of User Admins for tenant default",
      groupName: "user_admins",
    });

    const cognitoAppClient = cognitoUserPool.addClient(
      `derp-${tenant}-cognito-app-client`,
      {
        userPoolClientName: `derp-${tenant}-cognito-app-client`,
        accessTokenValidity: cdk.Duration.minutes(5),
        refreshTokenValidity: cdk.Duration.minutes(60),
        idTokenValidity: cdk.Duration.minutes(60),
        oAuth: {
          flows: {
            authorizationCodeGrant: true,
          },
          scopes: [cognito.OAuthScope.OPENID],
        },
        preventUserExistenceErrors: true,
      },
    );

    this.cognitoUserPoolId = cognitoUserPool.userPoolId;
    this.cognitoAppClientId = cognitoAppClient.userPoolClientId;
  }
}
