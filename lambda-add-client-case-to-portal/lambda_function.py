import base64
import json
import logging
import os
import sys
import uuid
from datetime import datetime
from uuid import UUID

import psycopg
import structlog
from pydantic import ValidationError

from schemas import Test

"""
Guide using Lambda with an RDS database:
https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/rds-lambda-tutorial.html#vpc-rds-create-deployment-package
"""
# rds settings
db_user = os.environ["DB_USER"]
db_password = os.environ["DB_PASSWORD"]
db_host = os.environ["DB_HOST"]
db_name = os.environ["DB_NAME"]
db_port = os.environ["DB_PORT"]

# Structlog configuration
logging.basicConfig(format="%(message)s", stream=sys.stdout, level=logging.INFO)
structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
        structlog.processors.TimeStamper(fmt="iso", utc=True),
        # structlog.dev.ConsoleRenderer(),
        structlog.processors.JSONRenderer(),
    ],
    logger_factory=structlog.PrintLoggerFactory(),
)

logger = structlog.get_logger()

try:
    conn = psycopg.connect(
        user=db_user, password=db_password, host=db_host, dbname=db_name, port=db_port
    )
except Exception as e:
    logger.error(str(e))
    raise Exception(str(e))
logger.info("SUCCESS: Connection to RDS PostgreSQL instance succeeded")


def lambda_handler(event, context):
    """Lambda handler.

    Args:
        event: Event triggered from external tenant SQS
        context: Lambda context

    Returns: None

    """
    event_body: str = event["Records"][0]["body"]
    event_body_data: dict = json.loads(event_body)
    message: str = event_body_data["Message"]
    data: dict = json.loads(message)

    structlog.contextvars.clear_contextvars()
    structlog.contextvars.bind_contextvars(
        request_id=str(uuid.uuid4()),
    )

    try:
        # Verify JSON structure using Pydantic, attempt to destructure JSON in Test schema
        test = Test(**data)
    except ValidationError as e:
        logger.error("Invalid Doge export schema %s", e.errors())
        return f"Invalid Doge export schema: {e.errors()}"

    structlog.contextvars.bind_contextvars(
        document_guid=test.document_guid,
        engagement_number=test.engagement_number,
        test_name=test.name,
    )
    with conn.cursor() as cur:
        # Retrieve the internal database ID of the ContextRiskRating scoring system
        cur.execute(
            "SELECT scoring_systems.id FROM scoring_systems "
            "WHERE scoring_systems.name=%s",
            (test.scoring_system.value,),
        )

        scoring_system_id = cur.fetchone()[0]

        current_datetime: datetime = datetime.now()
        # Insert test
        cur.execute(
            "INSERT INTO tests (id, engagement_number, name, scoring_system_id, time_of_last_export, export_status, export_error) "
            "VALUES(%s,%s,%s,%s,%s,%s,%s) "
            "ON CONFLICT(id) "
            "DO UPDATE SET engagement_number = %s, name = %s, time_of_last_export = %s RETURNING id",
            (
                test.document_guid,
                test.engagement_number,
                test.name,
                scoring_system_id,
                current_datetime,
                "OK",
                None,
                test.engagement_number,
                test.name,
                current_datetime,
            ),
        )

        inserted_test_id = cur.fetchone()[0]

        logger.info("Test stored")

        # Store all phases and findings guids for later processing
        json_phases_guids: set[UUID] = set()
        json_findings_guids: set[UUID] = set()

        # Insert or update phases
        for phase in test.phases:
            # Insert or update phase
            json_phases_guids.add(phase.guid)
            cur.execute(
                "INSERT INTO phases (guid, number, test_id, name) VALUES(%s,%s,%s,%s) "
                "ON CONFLICT(guid, test_id) DO UPDATE "
                "SET name = %s "
                "RETURNING id",
                (
                    phase.guid,
                    phase.phase_number,
                    inserted_test_id,
                    phase.name,
                    phase.name,
                ),
            )

            logger.info(
                "Phase stored",
                phase_guid=phase.guid,
                phase_name=phase.name,
                phase_number=phase.phase_number,
            )

            inserted_phase_id = cur.fetchone()[0]

            for finding in phase.findings:
                json_findings_guids.add(finding.guid)
                # Insert or update Findings

                # Check if there is a Finding with a Guid already associated with another Document Guid.
                # If there is, then something is wrong
                cur.execute(
                    "SELECT findings.id, findings.number, findings.phase_id, findings.name FROM findings "
                    "INNER JOIN public.phases p on p.id = findings.phase_id "
                    "INNER JOIN public.tests t on t.id = p.test_id "
                    "WHERE t.id != %s AND findings.id = %s",
                    (test.document_guid, finding.guid),
                )

                existing_findings_db_results = cur.fetchall()

                if len(existing_findings_db_results) > 0:
                    existing_findings_data: list[dict] = []
                    for existing_findings_db_result in existing_findings_db_results:
                        existing_finding_data: dict = {
                            "id": existing_findings_db_result[0],
                            "finding_number": existing_findings_db_result[1],
                            "phase_id": existing_findings_db_result[2],
                            "name": existing_findings_db_result[3],
                        }
                        existing_findings_data.append(existing_finding_data)
                    cur.execute(
                        "UPDATE tests "
                        "SET time_of_last_export = %s, export_status = %s, export_error = %s "
                        "WHERE tests.id = %s",
                        (
                            datetime.now(),
                            "ERROR",
                            f"Existing duplicate findings found: {existing_findings_data} ",
                            test.document_guid,
                        ),
                    )
                    conn.commit()
                    return

                cur.execute(
                    "INSERT INTO findings (id, number, phase_id, name, base_severity, vector_string, description, recommendation, supporting_material) "
                    "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s) "
                    "ON CONFLICT(id) DO UPDATE "
                    "SET number = %s, name = %s, base_severity = %s, vector_string = %s, description = %s, recommendation = %s, supporting_material = %s "
                    "RETURNING id",
                    (
                        finding.guid,
                        finding.finding_number,
                        inserted_phase_id,
                        finding.name,
                        finding.scoring.summary_rating,
                        finding.scoring.scoring_vector,
                        base64.b64decode(finding.description).decode("utf-8")
                        if finding.description is not None
                        else finding.description,
                        base64.b64decode(finding.recommendation).decode("utf-8")
                        if finding.recommendation is not None
                        else finding.recommendation,
                        base64.b64decode(finding.supporting_material).decode("utf-8")
                        if finding.supporting_material is not None
                        else finding.supporting_material,
                        # Update values
                        finding.finding_number,
                        finding.name,
                        finding.scoring.summary_rating,
                        finding.scoring.scoring_vector,
                        base64.b64decode(finding.description).decode("utf-8")
                        if finding.description is not None
                        else finding.description,
                        base64.b64decode(finding.recommendation).decode("utf-8")
                        if finding.recommendation is not None
                        else finding.recommendation,
                        base64.b64decode(finding.supporting_material).decode("utf-8")
                        if finding.supporting_material is not None
                        else finding.supporting_material,
                    ),
                )

                logger.info(
                    "Finding stored",
                    phase_guid=phase.guid,
                    phase_name=phase.name,
                    phase_number=phase.phase_number,
                    finding_guid=finding.guid,
                    finding_number=finding.finding_number,
                    finding_name=finding.name,
                )

                # Delete any previous Associated Hosts related to the Finding first
                cur.execute(
                    "DELETE FROM associated_hosts WHERE finding_id=%s", (finding.guid,)
                )

                # Then assign the newly exported Associated Hosts
                for host in finding.hosts:
                    cur.execute(
                        "INSERT INTO associated_hosts (finding_id, ip_address, network, port, protocol) "
                        "VALUES (%s, %s, %s, %s, %s) ",
                        (
                            finding.guid,
                            str(host.ip_address),
                            host.network,
                            host.port,
                            host.protocol,
                        ),
                    )

                    logger.info(
                        "Finding Associated Host stored",
                        phase_guid=phase.guid,
                        phase_name=phase.name,
                        phase_number=phase.phase_number,
                        finding_guid=finding.guid,
                        finding_number=finding.finding_number,
                        finding_name=finding.name,
                        host_ip_address=host.ip_address,
                        host_network=host.network,
                        host_port=host.port,
                        host_protocol=host.protocol,
                    )

        # Post-processing deletion step, for a Document Guid delete all Phases and Findings
        # that are in the database but not in the JSON

        if len(json_findings_guids) > 0:
            # Get all Findings Guids existing in the database but not in the incoming JSON
            cur.execute(
                "SELECT findings.id, findings.number FROM findings "
                "INNER JOIN public.phases p on p.id = findings.phase_id "
                "INNER JOIN public.tests t on t.id = p.test_id "
                "WHERE t.id=%s AND findings.id != ALL(%s)",
                (test.document_guid, list(json_findings_guids)),
            )
        else:
            cur.execute(
                "SELECT findings.id, findings.number FROM findings "
                "INNER JOIN public.phases p on p.id = findings.phase_id "
                "INNER JOIN public.tests t on t.id = p.test_id "
                "WHERE t.id=%s",
                (test.document_guid,),
            )
        extraneous_findings = cur.fetchall()
        extraneous_findings_ids: list[UUID] = []
        extraneous_findings_numbers: list[int] = []

        if len(extraneous_findings) > 0:
            logger.info("Extraneous Findings in export")
            for extraneous_finding in extraneous_findings:
                extraneous_findings_ids.append(extraneous_finding[0])
                extraneous_findings_numbers.append(extraneous_finding[1])

            # Delete extraneous Findings
            cur.execute(
                "DELETE FROM findings WHERE findings.id = ANY(%s)",
                (extraneous_findings_ids,),
            )

            logger.info(
                "Deleted extraneous Findings",
                extraneous_findings_ids=extraneous_findings_ids,
                extraneous_findings_numbers=extraneous_findings_numbers,
            )

            # Delete any extraneous Phases, any associated Findings have already been previously deleted
            # Get all Phases Guids existing in the database but not in the incoming JSON
            cur.execute(
                "SELECT phases.guid, phases.number FROM phases "
                "INNER JOIN public.tests t on t.id = phases.test_id "
                "WHERE t.id=%s AND phases.guid != ANY(%s)",
                (test.document_guid, list(json_phases_guids)),
            )
            extraneous_phase_guids: list[UUID] = []
            extraneous_phase_numbers: list[int] = []
            extraneous_phases = cur.fetchall()

            if len(extraneous_phases) > 0:
                for extraneous_phase in extraneous_phases:
                    extraneous_phase_guids.append(extraneous_phase[0])
                    extraneous_phase_numbers.append(extraneous_phase[1])

                # Delete extraneous Phases
                cur.execute(
                    "DELETE FROM phases WHERE phases.id = ANY(%s) AND phases.test_id = %s",
                    (extraneous_phase_guids, test.document_guid),
                )

                logger.info(
                    "Deleted extraneous Phases",
                    extraneous_phase_guids=extraneous_phase_guids,
                    extraneous_phase_numbers=extraneous_phase_numbers,
                )

    conn.commit()

    logger.info("Export complete")

    return "ok"
