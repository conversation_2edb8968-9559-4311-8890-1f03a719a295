[project]
name = "lambda-add-client-case-to-portal"
version = "0.1.0"
description = ""
authors = [
    { name = "<PERSON><PERSON> Panagiotopoulos", email = "<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "psycopg[binary]>=3.2.3",
    "pydantic>=2.9.2",
    "structlog>=24.4.0",
]

[dependency-groups]
dev = [
    "pytest>=8.3.4",
    "pytest-cov>=6.0.0",
    "pytest-dotenv>=0.5.2",
    "ruff>=0.8.3",
]

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.12
target-version = "py312"

[tool.ruff.lint]
select = [
    # Docstrings
    "D",
    # Pycodestyle errors
    "E",
    # Pyflakes
    "F",
    # pep8-naming
    "N",
    # Pycodestyle warnings
    "W",
    # isort
    "I001",
]
ignore = ["D100", "D101", "D104", "D206", "E501", "W191"]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[tool.ruff.lint.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.ruff.lint.pydocstyle]
# Use Google-style docstrings.
convention = "google"
