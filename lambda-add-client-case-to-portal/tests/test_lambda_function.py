import json
import os
from pathlib import Path

import psycopg
import pytest

from lambda_function import lambda_handler


@pytest.fixture
def context_risk_rating_event():
    """Returns a Context Risk Rating event as a fixture."""
    with open(
        Path(Path.cwd() / "tests" / "events" / "context_risk_rating_message.json")
    ) as f:
        event = f.read()
        event_body_data = {"Message": event}
        event_object = json.loads(event)
        event_body = {"Records": [{"body": json.dumps(event_body_data)}]}
        yield event_body, event_object


@pytest.fixture
def cvss3_event():
    """Returns a CVSS3 event as a fixture."""
    with open(Path(Path.cwd() / "tests" / "events" / "cvss3_message.json")) as f:
        event = f.read()
        event_body_data = {"Message": event}
        event_object = json.loads(event)
        event_body = {"Records": [{"body": json.dumps(event_body_data)}]}
        yield event_body, event_object


@pytest.fixture()
def database_connection():
    """Returns a database connection as a fixture."""
    db_user = os.environ["DB_USER"]
    db_password = os.environ["DB_PASSWORD"]
    db_host = os.environ["DB_HOST"]
    db_name = os.environ["DB_NAME"]
    db_port = os.environ["DB_PORT"]

    conn = psycopg.connect(
        user=db_user, password=db_password, host=db_host, dbname=db_name, port=db_port
    )
    yield conn
    conn.close()


def test_send_context_risk_rating_message(
    database_connection, context_risk_rating_event
):
    """Test storing a Context Risk Rating message to the Portal from an event JSON.

    Args:
        database_connection: Fixture containing a database connection
        context_risk_rating_event: Fixture emulating an event containing a message with a Context Risk Rating test
    Returns: None
    """
    lambda_handler(context_risk_rating_event[0], None)

    with database_connection.cursor() as cur:
        # Retrieve the internal database ID of the ContextRiskRating scoring system
        cur.execute(
            "SELECT tests.engagement_number, tests.id FROM tests WHERE tests.id=%s",
            (context_risk_rating_event[1]["document_guid"],),
        )

        results = cur.fetchall()

        assert len(results) == 1

        database_connection.commit()


def test_send_cvss3_message(database_connection, cvss3_event):
    """Test storing a CVSS3 message to the Portal from an event JSON.

    Args:
        database_connection: Fixture containing a database connection
        cvss3_event: Fixture emulating an event containing a message with a CVSSv3 test
    Returns: None
    """
    lambda_handler(cvss3_event[0], None)

    with database_connection.cursor() as cur:
        # Retrieve the internal database ID of the CVSS3 scoring system
        cur.execute(
            "SELECT tests.engagement_number, tests.id FROM tests WHERE tests.id=%s",
            (cvss3_event[1]["document_guid"],),
        )

        results = cur.fetchall()

        assert len(results) == 1

        database_connection.commit()
