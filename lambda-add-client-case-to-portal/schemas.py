from enum import StrEnum
from uuid import UUID

from pydantic import BaseModel, IPvAnyAddress


class Scoring(BaseModel):
    scoring_vector: str
    summary_rating: str


class Host(BaseModel):
    ip_address: IPvAnyAddress
    network: str
    port: int
    protocol: str


class Finding(BaseModel):
    finding_number: int
    guid: UUID
    name: str
    description: str | None
    recommendation: str | None
    supporting_material: str | None
    scoring: Scoring
    hosts: list[Host]


class Phase(BaseModel):
    phase_number: int
    guid: UUID
    name: str
    findings: list[Finding]


class ScoringSystem(StrEnum):
    ContextRiskRating = "ContextRiskRating"
    CVSS3Rating = "CVSS3Rating"
    CVSS31Rating = "CVSS31Rating"


class Test(BaseModel):
    engagement_number: int
    name: str
    client: str
    scoring_system: ScoringSystem
    document_guid: UUID
    engagement_guid: UUID
    phases: list[Phase]
